import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { createOrganization } from '../../utils/organizationService';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import Icon from '../../components/AppIcon';

const OrganizationSetup = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    domain: ''
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Organization name is required';
    } else if (formData.name.length < 2) {
      newErrors.name = 'Organization name must be at least 2 characters';
    }

    if (formData.domain && !/^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/.test(formData.domain)) {
      newErrors.domain = 'Please enter a valid domain (e.g., company.com)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear specific field error
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const result = await createOrganization({
        name: formData.name,
        description: formData.description || `${formData.name} workspace`,
        domain: formData.domain
      });

      if (result.data) {
        // Successfully created organization
        navigate('/role-based-dashboard', {
          state: {
            message: `Welcome to ${formData.name}! Your organization has been created successfully.`,
            type: 'success'
          }
        });
      } else {
        throw new Error(result.error || 'Failed to create organization');
      }
    } catch (error) {
      console.error('Organization creation error:', error);
      setErrors({ 
        submit: error.message || 'Failed to create organization. Please try again.' 
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSkip = () => {
    // Skip organization setup and go to dashboard
    navigate('/role-based-dashboard', {
      state: {
        message: 'You can create an organization later from the settings page.',
        type: 'info'
      }
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="bg-white rounded-lg shadow-enterprise border border-border p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="Building" size={32} className="text-primary" />
            </div>
            <h1 className="text-2xl font-semibold text-text-primary mb-2">
              Create Your Organization
            </h1>
            <p className="text-text-secondary">
              Set up your workspace to start collaborating with your team
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <Input
              label="Organization Name"
              type="text"
              placeholder="Enter your organization name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              error={errors.name}
              description="This will be the name of your workspace"
              required
            />

            <Input
              label="Description"
              type="text"
              placeholder="Brief description of your organization"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              error={errors.description}
              description="Optional: Help team members understand your organization's purpose"
            />

            <Input
              label="Domain"
              type="text"
              placeholder="company.com"
              value={formData.domain}
              onChange={(e) => handleInputChange('domain', e.target.value)}
              error={errors.domain}
              description="Optional: Used for email invitations and domain restrictions"
            />

            {errors.submit && (
              <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-md">
                <p className="text-sm text-destructive">{errors.submit}</p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="space-y-3">
              <Button
                type="submit"
                loading={isLoading}
                className="w-full"
                iconName="Building"
                iconPosition="left"
              >
                Create Organization
              </Button>
              
              <Button
                type="button"
                variant="ghost"
                onClick={handleSkip}
                className="w-full"
                disabled={isLoading}
              >
                Skip for now
              </Button>
            </div>
          </form>

          {/* Footer */}
          <div className="mt-8 pt-6 border-t border-border text-center">
            <p className="text-xs text-text-secondary">
              You can always create or join organizations later from your dashboard
            </p>
          </div>
        </div>

        {/* User Info */}
        {user && (
          <div className="mt-6 text-center">
            <p className="text-sm text-slate-600">
              Signed in as <span className="font-medium">{user.firstName} {user.lastName}</span>
            </p>
            <p className="text-xs text-slate-500">{user.email}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrganizationSetup;
