{"ast": null, "code": "var _jsxFileName = \"D:\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\role-based-dashboard\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { getOrganizations } from '../../utils/organizationService';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport DashboardHeader from './components/DashboardHeader';\nimport KPICard from './components/KPICard';\nimport ProjectCard from './components/ProjectCard';\nimport ActivityFeed from './components/ActivityFeed';\nimport QuickActions from './components/QuickActions';\nimport TaskSummary from './components/TaskSummary';\nimport TeamOverview from './components/TeamOverview';\nimport NotificationPanel from './components/NotificationPanel';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RoleBasedDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const location = useLocation();\n  const [userRole, setUserRole] = useState(null);\n  const [currentOrganization, setCurrentOrganization] = useState(null);\n  const [organizations, setOrganizations] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchValue, setSearchValue] = useState('');\n  const [filterValue, setFilterValue] = useState('all');\n  const [welcomeMessage, setWelcomeMessage] = useState(null);\n\n  // Fetch user organizations and role on component mount\n  useEffect(() => {\n    const fetchUserData = async () => {\n      if (!user) {\n        setLoading(false);\n        return;\n      }\n      try {\n        var _location$state, _location$state2;\n        setLoading(true);\n\n        // Get user's organizations\n        const orgsResult = await getOrganizations();\n        if (orgsResult.data && orgsResult.data.length > 0) {\n          setOrganizations(orgsResult.data);\n          const primaryOrg = orgsResult.data[0];\n          setCurrentOrganization(primaryOrg);\n          setUserRole(primaryOrg.role);\n          console.log('🔍 Dashboard: Loaded user role:', primaryOrg.role, 'for org:', primaryOrg.name);\n        } else {\n          console.warn('🔍 Dashboard: No organizations found for user');\n          setUserRole('member'); // Default fallback\n        }\n\n        // Check for welcome message from registration\n        if ((_location$state = location.state) !== null && _location$state !== void 0 && _location$state.message && (_location$state2 = location.state) !== null && _location$state2 !== void 0 && _location$state2.isNewUser) {\n          setWelcomeMessage({\n            text: location.state.message,\n            type: location.state.type || 'success'\n          });\n\n          // Clear the message after showing it\n          setTimeout(() => setWelcomeMessage(null), 5000);\n        }\n      } catch (error) {\n        console.error('🔍 Dashboard: Error fetching user data:', error);\n        setUserRole('member'); // Default fallback\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchUserData();\n  }, [user, location.state]);\n\n  // Mock data for different dashboard components\n  const mockProjects = [{\n    id: 1,\n    name: \"E-commerce Platform Redesign\",\n    description: \"Complete overhaul of the existing e-commerce platform with modern UI/UX design and improved performance optimization.\",\n    status: \"Active\",\n    priority: \"High\",\n    progress: 75,\n    dueDate: \"Dec 15, 2025\",\n    team: [{\n      name: \"Sarah Johnson\",\n      avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\"\n    }, {\n      name: \"Mike Chen\",\n      avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\"\n    }, {\n      name: \"Emily Davis\",\n      avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\"\n    }, {\n      name: \"Alex Rodriguez\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\"\n    }]\n  }, {\n    id: 2,\n    name: \"Mobile App Development\",\n    description: \"Native iOS and Android application development for customer engagement and loyalty program management.\",\n    status: \"Active\",\n    priority: \"Medium\",\n    progress: 45,\n    dueDate: \"Jan 30, 2026\",\n    team: [{\n      name: \"David Kim\",\n      avatar: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150\"\n    }, {\n      name: \"Lisa Wang\",\n      avatar: \"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150\"\n    }, {\n      name: \"Tom Wilson\",\n      avatar: \"https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150\"\n    }]\n  }, {\n    id: 3,\n    name: \"Data Analytics Dashboard\",\n    description: \"Business intelligence dashboard for real-time analytics and reporting with advanced data visualization capabilities.\",\n    status: \"Completed\",\n    priority: \"Low\",\n    progress: 100,\n    dueDate: \"Nov 20, 2025\",\n    team: [{\n      name: \"Rachel Green\",\n      avatar: \"https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150\"\n    }, {\n      name: \"James Brown\",\n      avatar: \"https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150\"\n    }]\n  }];\n  const mockActivities = [{\n    id: 1,\n    type: \"task_completed\",\n    user: {\n      name: \"Sarah Johnson\",\n      avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\"\n    },\n    description: \"Completed the user interface mockups for the checkout process\",\n    project: \"E-commerce Platform\",\n    timestamp: new Date(Date.now() - 15 * 60 * 1000),\n    isPublic: true\n  }, {\n    id: 2,\n    type: \"comment_added\",\n    user: {\n      name: \"Mike Chen\",\n      avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\"\n    },\n    description: \"Added feedback on the mobile responsive design implementation\",\n    project: \"E-commerce Platform\",\n    timestamp: new Date(Date.now() - 45 * 60 * 1000),\n    isPublic: true\n  }, {\n    id: 3,\n    type: \"project_created\",\n    user: {\n      name: \"Emily Davis\",\n      avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\"\n    },\n    description: \"Created new project for Q1 marketing campaign automation\",\n    project: \"Marketing Automation\",\n    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n    isPublic: false\n  }, {\n    id: 4,\n    type: \"member_added\",\n    user: {\n      name: \"Alex Rodriguez\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\"\n    },\n    description: \"Joined the mobile app development team as a senior developer\",\n    project: \"Mobile App Development\",\n    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),\n    isPublic: true\n  }];\n  const mockTasks = [{\n    id: 1,\n    title: \"Implement payment gateway integration\",\n    status: \"In Progress\",\n    priority: \"High\",\n    dueDate: \"Dec 10, 2025\",\n    assignee: \"Sarah Johnson\",\n    project: \"E-commerce Platform\"\n  }, {\n    id: 2,\n    title: \"Design user onboarding flow\",\n    status: \"To Do\",\n    priority: \"Medium\",\n    dueDate: \"Dec 12, 2025\",\n    assignee: \"Mike Chen\",\n    project: \"Mobile App\"\n  }, {\n    id: 3,\n    title: \"Set up automated testing pipeline\",\n    status: \"Review\",\n    priority: \"High\",\n    dueDate: \"Dec 8, 2025\",\n    assignee: \"Emily Davis\",\n    project: \"E-commerce Platform\"\n  }, {\n    id: 4,\n    title: \"Create API documentation\",\n    status: \"Done\",\n    priority: \"Low\",\n    dueDate: \"Dec 5, 2025\",\n    assignee: \"Alex Rodriguez\",\n    project: \"Data Analytics\"\n  }, {\n    id: 5,\n    title: \"Optimize database queries\",\n    status: \"Blocked\",\n    priority: \"High\",\n    dueDate: \"Dec 15, 2025\",\n    assignee: \"David Kim\",\n    project: \"E-commerce Platform\"\n  }];\n  const mockTeamMembers = [{\n    id: 1,\n    name: \"Sarah Johnson\",\n    email: \"<EMAIL>\",\n    role: \"Admin\",\n    status: \"Online\",\n    avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\",\n    department: \"Design\",\n    currentTask: \"UI/UX Design Review\",\n    tasksCompleted: 24,\n    lastActive: new Date()\n  }, {\n    id: 2,\n    name: \"Mike Chen\",\n    email: \"<EMAIL>\",\n    role: \"Member\",\n    status: \"Away\",\n    avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\",\n    department: \"Development\",\n    currentTask: \"Frontend Implementation\",\n    tasksCompleted: 18,\n    lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000)\n  }, {\n    id: 3,\n    name: \"Emily Davis\",\n    email: \"<EMAIL>\",\n    role: \"Owner\",\n    status: \"Online\",\n    avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\",\n    department: \"Management\",\n    currentTask: \"Project Planning\",\n    tasksCompleted: 32,\n    lastActive: new Date()\n  }, {\n    id: 4,\n    name: \"Alex Rodriguez\",\n    email: \"<EMAIL>\",\n    role: \"Member\",\n    status: \"Busy\",\n    avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\",\n    department: \"Development\",\n    currentTask: \"API Integration\",\n    tasksCompleted: 15,\n    lastActive: new Date(Date.now() - 30 * 60 * 1000)\n  }, {\n    id: 5,\n    name: \"David Kim\",\n    email: \"<EMAIL>\",\n    role: \"Viewer\",\n    status: \"Offline\",\n    avatar: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150\",\n    department: \"QA\",\n    currentTask: null,\n    tasksCompleted: 8,\n    lastActive: new Date(Date.now() - 24 * 60 * 60 * 1000)\n  }];\n  const mockNotifications = [{\n    id: 1,\n    type: \"task_assigned\",\n    title: \"New Task Assigned\",\n    message: \"You have been assigned to work on the payment gateway integration for the e-commerce platform.\",\n    timestamp: new Date(Date.now() - 30 * 60 * 1000),\n    read: false,\n    priority: \"high\",\n    user: {\n      name: \"Emily Davis\",\n      avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\"\n    },\n    actions: [{\n      label: \"View Task\",\n      variant: \"default\"\n    }, {\n      label: \"Accept\",\n      variant: \"outline\"\n    }]\n  }, {\n    id: 2,\n    type: \"deadline_reminder\",\n    title: \"Deadline Approaching\",\n    message: \"The UI/UX design review is due in 2 days. Please ensure all deliverables are ready for submission.\",\n    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n    read: false,\n    priority: \"medium\",\n    user: null,\n    actions: [{\n      label: \"View Details\",\n      variant: \"outline\"\n    }]\n  }, {\n    id: 3,\n    type: \"comment_mention\",\n    title: \"You were mentioned\",\n    message: \"Sarah Johnson mentioned you in a comment on the mobile app development project.\",\n    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),\n    read: true,\n    priority: \"low\",\n    user: {\n      name: \"Sarah Johnson\",\n      avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\"\n    },\n    actions: [{\n      label: \"Reply\",\n      variant: \"outline\"\n    }]\n  }, {\n    id: 4,\n    type: \"system_alert\",\n    title: \"System Maintenance\",\n    message: \"Scheduled maintenance will occur tonight from 11 PM to 1 AM EST. Some features may be temporarily unavailable.\",\n    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),\n    read: true,\n    priority: \"medium\",\n    user: null,\n    actions: []\n  }];\n\n  // Role-based KPI data\n  const getKPIData = () => {\n    switch (userRole) {\n      case 'Owner':\n        return [{\n          title: \"Total Revenue\",\n          value: \"$124,500\",\n          change: \"+12.5%\",\n          changeType: \"positive\",\n          icon: \"DollarSign\",\n          color: \"success\"\n        }, {\n          title: \"Active Projects\",\n          value: \"12\",\n          change: \"+3\",\n          changeType: \"positive\",\n          icon: \"FolderOpen\",\n          color: \"primary\"\n        }, {\n          title: \"Team Members\",\n          value: \"24\",\n          change: \"+2\",\n          changeType: \"positive\",\n          icon: \"Users\",\n          color: \"accent\"\n        }, {\n          title: \"Client Satisfaction\",\n          value: \"94%\",\n          change: \"+2%\",\n          changeType: \"positive\",\n          icon: \"Heart\",\n          color: \"success\"\n        }];\n      case 'Admin':\n        return [{\n          title: \"Active Projects\",\n          value: \"8\",\n          change: \"+2\",\n          changeType: \"positive\",\n          icon: \"FolderOpen\",\n          color: \"primary\"\n        }, {\n          title: \"Team Productivity\",\n          value: \"87%\",\n          change: \"+5%\",\n          changeType: \"positive\",\n          icon: \"TrendingUp\",\n          color: \"success\"\n        }, {\n          title: \"Pending Tasks\",\n          value: \"23\",\n          change: \"-4\",\n          changeType: \"positive\",\n          icon: \"CheckSquare\",\n          color: \"warning\"\n        }, {\n          title: \"Resource Utilization\",\n          value: \"92%\",\n          change: \"+3%\",\n          changeType: \"positive\",\n          icon: \"BarChart3\",\n          color: \"accent\"\n        }];\n      case 'Member':\n        return [{\n          title: \"My Tasks\",\n          value: \"8\",\n          change: \"+2\",\n          changeType: \"positive\",\n          icon: \"CheckSquare\",\n          color: \"primary\"\n        }, {\n          title: \"Completed Today\",\n          value: \"3\",\n          change: \"+1\",\n          changeType: \"positive\",\n          icon: \"Check\",\n          color: \"success\"\n        }, {\n          title: \"Hours Logged\",\n          value: \"32h\",\n          change: \"+4h\",\n          changeType: \"positive\",\n          icon: \"Clock\",\n          color: \"accent\"\n        }, {\n          title: \"Projects Involved\",\n          value: \"4\",\n          change: \"0\",\n          changeType: \"neutral\",\n          icon: \"FolderOpen\",\n          color: \"warning\"\n        }];\n      case 'Viewer':\n        return [{\n          title: \"Projects Viewed\",\n          value: \"6\",\n          change: \"+1\",\n          changeType: \"positive\",\n          icon: \"Eye\",\n          color: \"primary\"\n        }, {\n          title: \"Reports Generated\",\n          value: \"12\",\n          change: \"+3\",\n          changeType: \"positive\",\n          icon: \"FileText\",\n          color: \"accent\"\n        }, {\n          title: \"Data Exported\",\n          value: \"8\",\n          change: \"+2\",\n          changeType: \"positive\",\n          icon: \"Download\",\n          color: \"success\"\n        }, {\n          title: \"Dashboards Accessed\",\n          value: \"15\",\n          change: \"+5\",\n          changeType: \"positive\",\n          icon: \"BarChart3\",\n          color: \"warning\"\n        }];\n      default:\n        return [];\n    }\n  };\n\n  // Filter projects based on search and filter values\n  const filteredProjects = mockProjects.filter(project => {\n    const matchesSearch = project.name.toLowerCase().includes(searchValue.toLowerCase()) || project.description.toLowerCase().includes(searchValue.toLowerCase());\n    const matchesFilter = filterValue === 'all' || project.status.toLowerCase() === filterValue.toLowerCase();\n    return matchesSearch && matchesFilter;\n  });\n\n  // Role switcher for demo purposes\n  const handleRoleChange = newRole => {\n    setUserRole(newRole);\n  };\n\n  // Show loading state while fetching user data\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-slate-600\",\n          children: \"Loading your dashboard...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Show message if no role is determined\n  if (!userRole) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-slate-600 mb-4\",\n          children: \"Unable to determine your role. Please contact support.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/login\",\n          className: \"text-primary hover:text-primary/80\",\n          children: \"Return to Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20\",\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n      userRole: userRole.toLowerCase(),\n      currentUser: currentUser,\n      currentOrganization: currentOrganization\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 401,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"glass-effect border-b border-white/20 p-4 mt-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-slate-700\",\n            children: [\"Demo Mode - Current Role: \", userRole]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2\",\n          children: ['Owner', 'Admin', 'Member', 'Viewer'].map(role => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleRoleChange(role),\n            className: `px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${userRole === role ? 'bg-white text-slate-800 shadow-md' : 'text-slate-600 hover:bg-white/50 hover:text-slate-800'}`,\n            children: role\n          }, role, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DashboardHeader, {\n      userRole: userRole,\n      onFilterChange: setFilterValue,\n      onSearchChange: setSearchValue,\n      searchValue: searchValue\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 433,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10\",\n        children: getKPIData().map((kpi, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-fade-in\",\n          style: {\n            animationDelay: `${index * 0.1}s`\n          },\n          children: /*#__PURE__*/_jsxDEV(KPICard, {\n            title: kpi.title,\n            value: kpi.value,\n            change: kpi.change,\n            changeType: kpi.changeType,\n            icon: kpi.icon,\n            color: kpi.color\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 443,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2 space-y-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-2xl font-semibold text-slate-800 tracking-tight\",\n                  children: userRole === 'Viewer' ? 'Available Projects' : 'My Projects'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-slate-600 mt-1\",\n                  children: \"Manage and track your active projects\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/project-overview-analytics\",\n                className: \"flex items-center gap-2 px-4 py-2 text-sm text-blue-600 hover:text-blue-700 font-medium bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200\",\n                children: [\"View Analytics\", /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-4 h-4\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 5l7 7-7 7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 477,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 xl:grid-cols-2 gap-6\",\n              children: filteredProjects.map((project, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-fade-in\",\n                style: {\n                  animationDelay: `${index * 0.1}s`\n                },\n                children: /*#__PURE__*/_jsxDEV(ProjectCard, {\n                  project: project,\n                  userRole: userRole\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 21\n                }, this)\n              }, project.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 15\n            }, this), filteredProjects.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-16 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/20\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-8 h-8 text-slate-400\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 497,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-slate-600 text-lg\",\n                children: \"No projects match your current filters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-slate-500 text-sm mt-1\",\n                children: \"Try adjusting your search or filter criteria\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(QuickActions, {\n            userRole: userRole\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-8\",\n          children: [/*#__PURE__*/_jsxDEV(ActivityFeed, {\n            activities: mockActivities,\n            userRole: userRole\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NotificationPanel, {\n            notifications: mockNotifications,\n            userRole: userRole\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(TaskSummary, {\n          tasks: mockTasks,\n          userRole: userRole\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TeamOverview, {\n          teamMembers: mockTeamMembers,\n          userRole: userRole\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 520,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 399,\n    columnNumber: 5\n  }, this);\n};\n_s(RoleBasedDashboard, \"uPhVu7VCDLpqIYZ1tFXE8MbPMcM=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = RoleBasedDashboard;\nexport default RoleBasedDashboard;\nvar _c;\n$RefreshReg$(_c, \"RoleBasedDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLocation", "useAuth", "getOrganizations", "RoleBasedHeader", "DashboardHeader", "KPICard", "ProjectCard", "ActivityFeed", "QuickActions", "TaskSummary", "TeamOverview", "NotificationPanel", "jsxDEV", "_jsxDEV", "RoleBasedDashboard", "_s", "user", "location", "userRole", "setUserRole", "currentOrganization", "setCurrentOrganization", "organizations", "setOrganizations", "loading", "setLoading", "searchValue", "setSearchValue", "filterValue", "setFilterValue", "welcomeMessage", "setWelcomeMessage", "fetchUserData", "_location$state", "_location$state2", "orgsResult", "data", "length", "primaryOrg", "role", "console", "log", "name", "warn", "state", "message", "isNewUser", "text", "type", "setTimeout", "error", "mockProjects", "id", "description", "status", "priority", "progress", "dueDate", "team", "avatar", "mockActivities", "project", "timestamp", "Date", "now", "isPublic", "mockTasks", "title", "assignee", "mockTeamMembers", "email", "department", "currentTask", "tasksCompleted", "lastActive", "mockNotifications", "read", "actions", "label", "variant", "getKPIData", "value", "change", "changeType", "icon", "color", "filteredProjects", "filter", "matchesSearch", "toLowerCase", "includes", "matchesFilter", "handleRoleChange", "newRole", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "currentUser", "map", "onClick", "onFilterChange", "onSearchChange", "kpi", "index", "style", "animationDelay", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "activities", "notifications", "tasks", "teamMembers", "_c", "$RefreshReg$"], "sources": ["D:/PM/agnoworksphere/src/pages/role-based-dashboard/index.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { getOrganizations } from '../../utils/organizationService';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport DashboardHeader from './components/DashboardHeader';\nimport KPICard from './components/KPICard';\nimport ProjectCard from './components/ProjectCard';\nimport ActivityFeed from './components/ActivityFeed';\nimport QuickActions from './components/QuickActions';\nimport TaskSummary from './components/TaskSummary';\nimport TeamOverview from './components/TeamOverview';\nimport NotificationPanel from './components/NotificationPanel';\n\nconst RoleBasedDashboard = () => {\n  const { user } = useAuth();\n  const location = useLocation();\n  const [userRole, setUserRole] = useState(null);\n  const [currentOrganization, setCurrentOrganization] = useState(null);\n  const [organizations, setOrganizations] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchValue, setSearchValue] = useState('');\n  const [filterValue, setFilterValue] = useState('all');\n  const [welcomeMessage, setWelcomeMessage] = useState(null);\n\n  // Fetch user organizations and role on component mount\n  useEffect(() => {\n    const fetchUserData = async () => {\n      if (!user) {\n        setLoading(false);\n        return;\n      }\n\n      try {\n        setLoading(true);\n\n        // Get user's organizations\n        const orgsResult = await getOrganizations();\n\n        if (orgsResult.data && orgsResult.data.length > 0) {\n          setOrganizations(orgsResult.data);\n          const primaryOrg = orgsResult.data[0];\n          setCurrentOrganization(primaryOrg);\n          setUserRole(primaryOrg.role);\n\n          console.log('🔍 Dashboard: Loaded user role:', primaryOrg.role, 'for org:', primaryOrg.name);\n        } else {\n          console.warn('🔍 Dashboard: No organizations found for user');\n          setUserRole('member'); // Default fallback\n        }\n\n        // Check for welcome message from registration\n        if (location.state?.message && location.state?.isNewUser) {\n          setWelcomeMessage({\n            text: location.state.message,\n            type: location.state.type || 'success'\n          });\n\n          // Clear the message after showing it\n          setTimeout(() => setWelcomeMessage(null), 5000);\n        }\n\n      } catch (error) {\n        console.error('🔍 Dashboard: Error fetching user data:', error);\n        setUserRole('member'); // Default fallback\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchUserData();\n  }, [user, location.state]);\n\n  // Mock data for different dashboard components\n  const mockProjects = [\n    {\n      id: 1,\n      name: \"E-commerce Platform Redesign\",\n      description: \"Complete overhaul of the existing e-commerce platform with modern UI/UX design and improved performance optimization.\",\n      status: \"Active\",\n      priority: \"High\",\n      progress: 75,\n      dueDate: \"Dec 15, 2025\",\n      team: [\n        { name: \"Sarah Johnson\", avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\" },\n        { name: \"Mike Chen\", avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\" },\n        { name: \"Emily Davis\", avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\" },\n        { name: \"Alex Rodriguez\", avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\" }\n      ]\n    },\n    {\n      id: 2,\n      name: \"Mobile App Development\",\n      description: \"Native iOS and Android application development for customer engagement and loyalty program management.\",\n      status: \"Active\",\n      priority: \"Medium\",\n      progress: 45,\n      dueDate: \"Jan 30, 2026\",\n      team: [\n        { name: \"David Kim\", avatar: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150\" },\n        { name: \"Lisa Wang\", avatar: \"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150\" },\n        { name: \"Tom Wilson\", avatar: \"https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150\" }\n      ]\n    },\n    {\n      id: 3,\n      name: \"Data Analytics Dashboard\",\n      description: \"Business intelligence dashboard for real-time analytics and reporting with advanced data visualization capabilities.\",\n      status: \"Completed\",\n      priority: \"Low\",\n      progress: 100,\n      dueDate: \"Nov 20, 2025\",\n      team: [\n        { name: \"Rachel Green\", avatar: \"https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150\" },\n        { name: \"James Brown\", avatar: \"https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150\" }\n      ]\n    }\n  ];\n\n  const mockActivities = [\n    {\n      id: 1,\n      type: \"task_completed\",\n      user: { name: \"Sarah Johnson\", avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\" },\n      description: \"Completed the user interface mockups for the checkout process\",\n      project: \"E-commerce Platform\",\n      timestamp: new Date(Date.now() - 15 * 60 * 1000),\n      isPublic: true\n    },\n    {\n      id: 2,\n      type: \"comment_added\",\n      user: { name: \"Mike Chen\", avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\" },\n      description: \"Added feedback on the mobile responsive design implementation\",\n      project: \"E-commerce Platform\",\n      timestamp: new Date(Date.now() - 45 * 60 * 1000),\n      isPublic: true\n    },\n    {\n      id: 3,\n      type: \"project_created\",\n      user: { name: \"Emily Davis\", avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\" },\n      description: \"Created new project for Q1 marketing campaign automation\",\n      project: \"Marketing Automation\",\n      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n      isPublic: false\n    },\n    {\n      id: 4,\n      type: \"member_added\",\n      user: { name: \"Alex Rodriguez\", avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\" },\n      description: \"Joined the mobile app development team as a senior developer\",\n      project: \"Mobile App Development\",\n      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),\n      isPublic: true\n    }\n  ];\n\n  const mockTasks = [\n    {\n      id: 1,\n      title: \"Implement payment gateway integration\",\n      status: \"In Progress\",\n      priority: \"High\",\n      dueDate: \"Dec 10, 2025\",\n      assignee: \"Sarah Johnson\",\n      project: \"E-commerce Platform\"\n    },\n    {\n      id: 2,\n      title: \"Design user onboarding flow\",\n      status: \"To Do\",\n      priority: \"Medium\",\n      dueDate: \"Dec 12, 2025\",\n      assignee: \"Mike Chen\",\n      project: \"Mobile App\"\n    },\n    {\n      id: 3,\n      title: \"Set up automated testing pipeline\",\n      status: \"Review\",\n      priority: \"High\",\n      dueDate: \"Dec 8, 2025\",\n      assignee: \"Emily Davis\",\n      project: \"E-commerce Platform\"\n    },\n    {\n      id: 4,\n      title: \"Create API documentation\",\n      status: \"Done\",\n      priority: \"Low\",\n      dueDate: \"Dec 5, 2025\",\n      assignee: \"Alex Rodriguez\",\n      project: \"Data Analytics\"\n    },\n    {\n      id: 5,\n      title: \"Optimize database queries\",\n      status: \"Blocked\",\n      priority: \"High\",\n      dueDate: \"Dec 15, 2025\",\n      assignee: \"David Kim\",\n      project: \"E-commerce Platform\"\n    }\n  ];\n\n  const mockTeamMembers = [\n    {\n      id: 1,\n      name: \"Sarah Johnson\",\n      email: \"<EMAIL>\",\n      role: \"Admin\",\n      status: \"Online\",\n      avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\",\n      department: \"Design\",\n      currentTask: \"UI/UX Design Review\",\n      tasksCompleted: 24,\n      lastActive: new Date()\n    },\n    {\n      id: 2,\n      name: \"Mike Chen\",\n      email: \"<EMAIL>\",\n      role: \"Member\",\n      status: \"Away\",\n      avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\",\n      department: \"Development\",\n      currentTask: \"Frontend Implementation\",\n      tasksCompleted: 18,\n      lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000)\n    },\n    {\n      id: 3,\n      name: \"Emily Davis\",\n      email: \"<EMAIL>\",\n      role: \"Owner\",\n      status: \"Online\",\n      avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\",\n      department: \"Management\",\n      currentTask: \"Project Planning\",\n      tasksCompleted: 32,\n      lastActive: new Date()\n    },\n    {\n      id: 4,\n      name: \"Alex Rodriguez\",\n      email: \"<EMAIL>\",\n      role: \"Member\",\n      status: \"Busy\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\",\n      department: \"Development\",\n      currentTask: \"API Integration\",\n      tasksCompleted: 15,\n      lastActive: new Date(Date.now() - 30 * 60 * 1000)\n    },\n    {\n      id: 5,\n      name: \"David Kim\",\n      email: \"<EMAIL>\",\n      role: \"Viewer\",\n      status: \"Offline\",\n      avatar: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150\",\n      department: \"QA\",\n      currentTask: null,\n      tasksCompleted: 8,\n      lastActive: new Date(Date.now() - 24 * 60 * 60 * 1000)\n    }\n  ];\n\n  const mockNotifications = [\n    {\n      id: 1,\n      type: \"task_assigned\",\n      title: \"New Task Assigned\",\n      message: \"You have been assigned to work on the payment gateway integration for the e-commerce platform.\",\n      timestamp: new Date(Date.now() - 30 * 60 * 1000),\n      read: false,\n      priority: \"high\",\n      user: { name: \"Emily Davis\", avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\" },\n      actions: [\n        { label: \"View Task\", variant: \"default\" },\n        { label: \"Accept\", variant: \"outline\" }\n      ]\n    },\n    {\n      id: 2,\n      type: \"deadline_reminder\",\n      title: \"Deadline Approaching\",\n      message: \"The UI/UX design review is due in 2 days. Please ensure all deliverables are ready for submission.\",\n      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n      read: false,\n      priority: \"medium\",\n      user: null,\n      actions: [\n        { label: \"View Details\", variant: \"outline\" }\n      ]\n    },\n    {\n      id: 3,\n      type: \"comment_mention\",\n      title: \"You were mentioned\",\n      message: \"Sarah Johnson mentioned you in a comment on the mobile app development project.\",\n      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),\n      read: true,\n      priority: \"low\",\n      user: { name: \"Sarah Johnson\", avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\" },\n      actions: [\n        { label: \"Reply\", variant: \"outline\" }\n      ]\n    },\n    {\n      id: 4,\n      type: \"system_alert\",\n      title: \"System Maintenance\",\n      message: \"Scheduled maintenance will occur tonight from 11 PM to 1 AM EST. Some features may be temporarily unavailable.\",\n      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),\n      read: true,\n      priority: \"medium\",\n      user: null,\n      actions: []\n    }\n  ];\n\n  // Role-based KPI data\n  const getKPIData = () => {\n    switch (userRole) {\n      case 'Owner':\n        return [\n          { title: \"Total Revenue\", value: \"$124,500\", change: \"+12.5%\", changeType: \"positive\", icon: \"DollarSign\", color: \"success\" },\n          { title: \"Active Projects\", value: \"12\", change: \"+3\", changeType: \"positive\", icon: \"FolderOpen\", color: \"primary\" },\n          { title: \"Team Members\", value: \"24\", change: \"+2\", changeType: \"positive\", icon: \"Users\", color: \"accent\" },\n          { title: \"Client Satisfaction\", value: \"94%\", change: \"+2%\", changeType: \"positive\", icon: \"Heart\", color: \"success\" }\n        ];\n      case 'Admin':\n        return [\n          { title: \"Active Projects\", value: \"8\", change: \"+2\", changeType: \"positive\", icon: \"FolderOpen\", color: \"primary\" },\n          { title: \"Team Productivity\", value: \"87%\", change: \"+5%\", changeType: \"positive\", icon: \"TrendingUp\", color: \"success\" },\n          { title: \"Pending Tasks\", value: \"23\", change: \"-4\", changeType: \"positive\", icon: \"CheckSquare\", color: \"warning\" },\n          { title: \"Resource Utilization\", value: \"92%\", change: \"+3%\", changeType: \"positive\", icon: \"BarChart3\", color: \"accent\" }\n        ];\n      case 'Member':\n        return [\n          { title: \"My Tasks\", value: \"8\", change: \"+2\", changeType: \"positive\", icon: \"CheckSquare\", color: \"primary\" },\n          { title: \"Completed Today\", value: \"3\", change: \"+1\", changeType: \"positive\", icon: \"Check\", color: \"success\" },\n          { title: \"Hours Logged\", value: \"32h\", change: \"+4h\", changeType: \"positive\", icon: \"Clock\", color: \"accent\" },\n          { title: \"Projects Involved\", value: \"4\", change: \"0\", changeType: \"neutral\", icon: \"FolderOpen\", color: \"warning\" }\n        ];\n      case 'Viewer':\n        return [\n          { title: \"Projects Viewed\", value: \"6\", change: \"+1\", changeType: \"positive\", icon: \"Eye\", color: \"primary\" },\n          { title: \"Reports Generated\", value: \"12\", change: \"+3\", changeType: \"positive\", icon: \"FileText\", color: \"accent\" },\n          { title: \"Data Exported\", value: \"8\", change: \"+2\", changeType: \"positive\", icon: \"Download\", color: \"success\" },\n          { title: \"Dashboards Accessed\", value: \"15\", change: \"+5\", changeType: \"positive\", icon: \"BarChart3\", color: \"warning\" }\n        ];\n      default:\n        return [];\n    }\n  };\n\n  // Filter projects based on search and filter values\n  const filteredProjects = mockProjects.filter(project => {\n    const matchesSearch = project.name.toLowerCase().includes(searchValue.toLowerCase()) ||\n                         project.description.toLowerCase().includes(searchValue.toLowerCase());\n    const matchesFilter = filterValue === 'all' || \n                         project.status.toLowerCase() === filterValue.toLowerCase();\n    return matchesSearch && matchesFilter;\n  });\n\n  // Role switcher for demo purposes\n  const handleRoleChange = (newRole) => {\n    setUserRole(newRole);\n  };\n\n  // Show loading state while fetching user data\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"></div>\n          <p className=\"text-slate-600\">Loading your dashboard...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Show message if no role is determined\n  if (!userRole) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <p className=\"text-slate-600 mb-4\">Unable to determine your role. Please contact support.</p>\n          <Link to=\"/login\" className=\"text-primary hover:text-primary/80\">Return to Login</Link>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20\">\n      {/* Role-Based Header */}\n      <RoleBasedHeader\n        userRole={userRole.toLowerCase()}\n        currentUser={currentUser}\n        currentOrganization={currentOrganization}\n      />\n\n      {/* Enhanced Demo Role Switcher */}\n      <div className=\"glass-effect border-b border-white/20 p-4 mt-16\">\n        <div className=\"max-w-7xl mx-auto flex items-center justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n            <span className=\"text-sm font-medium text-slate-700\">Demo Mode - Current Role: {userRole}</span>\n          </div>\n          <div className=\"flex items-center gap-2\">\n            {['Owner', 'Admin', 'Member', 'Viewer'].map((role) => (\n              <button\n                key={role}\n                onClick={() => handleRoleChange(role)}\n                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${\n                  userRole === role\n                    ? 'bg-white text-slate-800 shadow-md'\n                    : 'text-slate-600 hover:bg-white/50 hover:text-slate-800'\n                }`}\n              >\n                {role}\n              </button>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Dashboard Header */}\n      <DashboardHeader\n        userRole={userRole}\n        onFilterChange={setFilterValue}\n        onSearchChange={setSearchValue}\n        searchValue={searchValue}\n      />\n\n      {/* Main Dashboard Content */}\n      <div className=\"max-w-7xl mx-auto p-8\">\n        {/* Enhanced KPI Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10\">\n          {getKPIData().map((kpi, index) => (\n            <div key={index} className=\"animate-fade-in\" style={{ animationDelay: `${index * 0.1}s` }}>\n              <KPICard\n                title={kpi.title}\n                value={kpi.value}\n                change={kpi.change}\n                changeType={kpi.changeType}\n                icon={kpi.icon}\n                color={kpi.color}\n              />\n            </div>\n          ))}\n        </div>\n\n        {/* Main Content Grid with improved spacing */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-10\">\n          {/* Left Column - Projects and Quick Actions */}\n          <div className=\"lg:col-span-2 space-y-8\">\n            {/* Projects Section with enhanced header */}\n            <div className=\"space-y-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h2 className=\"text-2xl font-semibold text-slate-800 tracking-tight\">\n                    {userRole === 'Viewer' ? 'Available Projects' : 'My Projects'}\n                  </h2>\n                  <p className=\"text-slate-600 mt-1\">Manage and track your active projects</p>\n                </div>\n                <Link \n                  to=\"/project-overview-analytics\"\n                  className=\"flex items-center gap-2 px-4 py-2 text-sm text-blue-600 hover:text-blue-700 font-medium bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200\"\n                >\n                  View Analytics\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                  </svg>\n                </Link>\n              </div>\n              \n              <div className=\"grid grid-cols-1 xl:grid-cols-2 gap-6\">\n                {filteredProjects.map((project, index) => (\n                  <div key={project.id} className=\"animate-fade-in\" style={{ animationDelay: `${index * 0.1}s` }}>\n                    <ProjectCard\n                      project={project}\n                      userRole={userRole}\n                    />\n                  </div>\n                ))}\n              </div>\n              \n              {filteredProjects.length === 0 && (\n                <div className=\"text-center py-16 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/20\">\n                  <div className=\"w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <svg className=\"w-8 h-8 text-slate-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                    </svg>\n                  </div>\n                  <p className=\"text-slate-600 text-lg\">No projects match your current filters</p>\n                  <p className=\"text-slate-500 text-sm mt-1\">Try adjusting your search or filter criteria</p>\n                </div>\n              )}\n            </div>\n\n            {/* Quick Actions */}\n            <QuickActions userRole={userRole} />\n          </div>\n\n          {/* Right Column - Activity Feed and Notifications */}\n          <div className=\"space-y-8\">\n            <ActivityFeed activities={mockActivities} userRole={userRole} />\n            <NotificationPanel notifications={mockNotifications} userRole={userRole} />\n          </div>\n        </div>\n\n        {/* Bottom Section - Tasks and Team with improved layout */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          <TaskSummary tasks={mockTasks} userRole={userRole} />\n          <TeamOverview teamMembers={mockTeamMembers} userRole={userRole} />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RoleBasedDashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,iBAAiB,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC;EAAK,CAAC,GAAGf,OAAO,CAAC,CAAC;EAC1B,MAAMgB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACuB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACyB,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;;EAE1D;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkC,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI,CAAChB,IAAI,EAAE;QACTS,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,IAAI;QAAA,IAAAQ,eAAA,EAAAC,gBAAA;QACFT,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,MAAMU,UAAU,GAAG,MAAMjC,gBAAgB,CAAC,CAAC;QAE3C,IAAIiC,UAAU,CAACC,IAAI,IAAID,UAAU,CAACC,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;UACjDd,gBAAgB,CAACY,UAAU,CAACC,IAAI,CAAC;UACjC,MAAME,UAAU,GAAGH,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC;UACrCf,sBAAsB,CAACiB,UAAU,CAAC;UAClCnB,WAAW,CAACmB,UAAU,CAACC,IAAI,CAAC;UAE5BC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEH,UAAU,CAACC,IAAI,EAAE,UAAU,EAAED,UAAU,CAACI,IAAI,CAAC;QAC9F,CAAC,MAAM;UACLF,OAAO,CAACG,IAAI,CAAC,+CAA+C,CAAC;UAC7DxB,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;QACzB;;QAEA;QACA,IAAI,CAAAc,eAAA,GAAAhB,QAAQ,CAAC2B,KAAK,cAAAX,eAAA,eAAdA,eAAA,CAAgBY,OAAO,KAAAX,gBAAA,GAAIjB,QAAQ,CAAC2B,KAAK,cAAAV,gBAAA,eAAdA,gBAAA,CAAgBY,SAAS,EAAE;UACxDf,iBAAiB,CAAC;YAChBgB,IAAI,EAAE9B,QAAQ,CAAC2B,KAAK,CAACC,OAAO;YAC5BG,IAAI,EAAE/B,QAAQ,CAAC2B,KAAK,CAACI,IAAI,IAAI;UAC/B,CAAC,CAAC;;UAEF;UACAC,UAAU,CAAC,MAAMlB,iBAAiB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;QACjD;MAEF,CAAC,CAAC,OAAOmB,KAAK,EAAE;QACdV,OAAO,CAACU,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D/B,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;MACzB,CAAC,SAAS;QACRM,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDO,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAAChB,IAAI,EAAEC,QAAQ,CAAC2B,KAAK,CAAC,CAAC;;EAE1B;EACA,MAAMO,YAAY,GAAG,CACnB;IACEC,EAAE,EAAE,CAAC;IACLV,IAAI,EAAE,8BAA8B;IACpCW,WAAW,EAAE,uHAAuH;IACpIC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,cAAc;IACvBC,IAAI,EAAE,CACJ;MAAEhB,IAAI,EAAE,eAAe;MAAEiB,MAAM,EAAE;IAAqE,CAAC,EACvG;MAAEjB,IAAI,EAAE,WAAW;MAAEiB,MAAM,EAAE;IAAqE,CAAC,EACnG;MAAEjB,IAAI,EAAE,aAAa;MAAEiB,MAAM,EAAE;IAAqE,CAAC,EACrG;MAAEjB,IAAI,EAAE,gBAAgB;MAAEiB,MAAM,EAAE;IAAqE,CAAC;EAE5G,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLV,IAAI,EAAE,wBAAwB;IAC9BW,WAAW,EAAE,wGAAwG;IACrHC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,cAAc;IACvBC,IAAI,EAAE,CACJ;MAAEhB,IAAI,EAAE,WAAW;MAAEiB,MAAM,EAAE;IAAqE,CAAC,EACnG;MAAEjB,IAAI,EAAE,WAAW;MAAEiB,MAAM,EAAE;IAAkE,CAAC,EAChG;MAAEjB,IAAI,EAAE,YAAY;MAAEiB,MAAM,EAAE;IAAqE,CAAC;EAExG,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLV,IAAI,EAAE,0BAA0B;IAChCW,WAAW,EAAE,sHAAsH;IACnIC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,GAAG;IACbC,OAAO,EAAE,cAAc;IACvBC,IAAI,EAAE,CACJ;MAAEhB,IAAI,EAAE,cAAc;MAAEiB,MAAM,EAAE;IAAqE,CAAC,EACtG;MAAEjB,IAAI,EAAE,aAAa;MAAEiB,MAAM,EAAE;IAAqE,CAAC;EAEzG,CAAC,CACF;EAED,MAAMC,cAAc,GAAG,CACrB;IACER,EAAE,EAAE,CAAC;IACLJ,IAAI,EAAE,gBAAgB;IACtBhC,IAAI,EAAE;MAAE0B,IAAI,EAAE,eAAe;MAAEiB,MAAM,EAAE;IAAqE,CAAC;IAC7GN,WAAW,EAAE,+DAA+D;IAC5EQ,OAAO,EAAE,qBAAqB;IAC9BC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAChDC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEb,EAAE,EAAE,CAAC;IACLJ,IAAI,EAAE,eAAe;IACrBhC,IAAI,EAAE;MAAE0B,IAAI,EAAE,WAAW;MAAEiB,MAAM,EAAE;IAAqE,CAAC;IACzGN,WAAW,EAAE,+DAA+D;IAC5EQ,OAAO,EAAE,qBAAqB;IAC9BC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAChDC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEb,EAAE,EAAE,CAAC;IACLJ,IAAI,EAAE,iBAAiB;IACvBhC,IAAI,EAAE;MAAE0B,IAAI,EAAE,aAAa;MAAEiB,MAAM,EAAE;IAAqE,CAAC;IAC3GN,WAAW,EAAE,0DAA0D;IACvEQ,OAAO,EAAE,sBAAsB;IAC/BC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpDC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEb,EAAE,EAAE,CAAC;IACLJ,IAAI,EAAE,cAAc;IACpBhC,IAAI,EAAE;MAAE0B,IAAI,EAAE,gBAAgB;MAAEiB,MAAM,EAAE;IAAqE,CAAC;IAC9GN,WAAW,EAAE,8DAA8D;IAC3EQ,OAAO,EAAE,wBAAwB;IACjCC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpDC,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,MAAMC,SAAS,GAAG,CAChB;IACEd,EAAE,EAAE,CAAC;IACLe,KAAK,EAAE,uCAAuC;IAC9Cb,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,MAAM;IAChBE,OAAO,EAAE,cAAc;IACvBW,QAAQ,EAAE,eAAe;IACzBP,OAAO,EAAE;EACX,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLe,KAAK,EAAE,6BAA6B;IACpCb,MAAM,EAAE,OAAO;IACfC,QAAQ,EAAE,QAAQ;IAClBE,OAAO,EAAE,cAAc;IACvBW,QAAQ,EAAE,WAAW;IACrBP,OAAO,EAAE;EACX,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLe,KAAK,EAAE,mCAAmC;IAC1Cb,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,MAAM;IAChBE,OAAO,EAAE,aAAa;IACtBW,QAAQ,EAAE,aAAa;IACvBP,OAAO,EAAE;EACX,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLe,KAAK,EAAE,0BAA0B;IACjCb,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,KAAK;IACfE,OAAO,EAAE,aAAa;IACtBW,QAAQ,EAAE,gBAAgB;IAC1BP,OAAO,EAAE;EACX,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLe,KAAK,EAAE,2BAA2B;IAClCb,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBE,OAAO,EAAE,cAAc;IACvBW,QAAQ,EAAE,WAAW;IACrBP,OAAO,EAAE;EACX,CAAC,CACF;EAED,MAAMQ,eAAe,GAAG,CACtB;IACEjB,EAAE,EAAE,CAAC;IACLV,IAAI,EAAE,eAAe;IACrB4B,KAAK,EAAE,wBAAwB;IAC/B/B,IAAI,EAAE,OAAO;IACbe,MAAM,EAAE,QAAQ;IAChBK,MAAM,EAAE,oEAAoE;IAC5EY,UAAU,EAAE,QAAQ;IACpBC,WAAW,EAAE,qBAAqB;IAClCC,cAAc,EAAE,EAAE;IAClBC,UAAU,EAAE,IAAIX,IAAI,CAAC;EACvB,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLV,IAAI,EAAE,WAAW;IACjB4B,KAAK,EAAE,oBAAoB;IAC3B/B,IAAI,EAAE,QAAQ;IACde,MAAM,EAAE,MAAM;IACdK,MAAM,EAAE,oEAAoE;IAC5EY,UAAU,EAAE,aAAa;IACzBC,WAAW,EAAE,yBAAyB;IACtCC,cAAc,EAAE,EAAE;IAClBC,UAAU,EAAE,IAAIX,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;EACtD,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLV,IAAI,EAAE,aAAa;IACnB4B,KAAK,EAAE,sBAAsB;IAC7B/B,IAAI,EAAE,OAAO;IACbe,MAAM,EAAE,QAAQ;IAChBK,MAAM,EAAE,oEAAoE;IAC5EY,UAAU,EAAE,YAAY;IACxBC,WAAW,EAAE,kBAAkB;IAC/BC,cAAc,EAAE,EAAE;IAClBC,UAAU,EAAE,IAAIX,IAAI,CAAC;EACvB,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLV,IAAI,EAAE,gBAAgB;IACtB4B,KAAK,EAAE,yBAAyB;IAChC/B,IAAI,EAAE,QAAQ;IACde,MAAM,EAAE,MAAM;IACdK,MAAM,EAAE,oEAAoE;IAC5EY,UAAU,EAAE,aAAa;IACzBC,WAAW,EAAE,iBAAiB;IAC9BC,cAAc,EAAE,EAAE;IAClBC,UAAU,EAAE,IAAIX,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;EAClD,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLV,IAAI,EAAE,WAAW;IACjB4B,KAAK,EAAE,oBAAoB;IAC3B/B,IAAI,EAAE,QAAQ;IACde,MAAM,EAAE,SAAS;IACjBK,MAAM,EAAE,oEAAoE;IAC5EY,UAAU,EAAE,IAAI;IAChBC,WAAW,EAAE,IAAI;IACjBC,cAAc,EAAE,CAAC;IACjBC,UAAU,EAAE,IAAIX,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;EACvD,CAAC,CACF;EAED,MAAMW,iBAAiB,GAAG,CACxB;IACEvB,EAAE,EAAE,CAAC;IACLJ,IAAI,EAAE,eAAe;IACrBmB,KAAK,EAAE,mBAAmB;IAC1BtB,OAAO,EAAE,gGAAgG;IACzGiB,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAChDY,IAAI,EAAE,KAAK;IACXrB,QAAQ,EAAE,MAAM;IAChBvC,IAAI,EAAE;MAAE0B,IAAI,EAAE,aAAa;MAAEiB,MAAM,EAAE;IAAqE,CAAC;IAC3GkB,OAAO,EAAE,CACP;MAAEC,KAAK,EAAE,WAAW;MAAEC,OAAO,EAAE;IAAU,CAAC,EAC1C;MAAED,KAAK,EAAE,QAAQ;MAAEC,OAAO,EAAE;IAAU,CAAC;EAE3C,CAAC,EACD;IACE3B,EAAE,EAAE,CAAC;IACLJ,IAAI,EAAE,mBAAmB;IACzBmB,KAAK,EAAE,sBAAsB;IAC7BtB,OAAO,EAAE,oGAAoG;IAC7GiB,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpDY,IAAI,EAAE,KAAK;IACXrB,QAAQ,EAAE,QAAQ;IAClBvC,IAAI,EAAE,IAAI;IACV6D,OAAO,EAAE,CACP;MAAEC,KAAK,EAAE,cAAc;MAAEC,OAAO,EAAE;IAAU,CAAC;EAEjD,CAAC,EACD;IACE3B,EAAE,EAAE,CAAC;IACLJ,IAAI,EAAE,iBAAiB;IACvBmB,KAAK,EAAE,oBAAoB;IAC3BtB,OAAO,EAAE,iFAAiF;IAC1FiB,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpDY,IAAI,EAAE,IAAI;IACVrB,QAAQ,EAAE,KAAK;IACfvC,IAAI,EAAE;MAAE0B,IAAI,EAAE,eAAe;MAAEiB,MAAM,EAAE;IAAqE,CAAC;IAC7GkB,OAAO,EAAE,CACP;MAAEC,KAAK,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAU,CAAC;EAE1C,CAAC,EACD;IACE3B,EAAE,EAAE,CAAC;IACLJ,IAAI,EAAE,cAAc;IACpBmB,KAAK,EAAE,oBAAoB;IAC3BtB,OAAO,EAAE,gHAAgH;IACzHiB,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpDY,IAAI,EAAE,IAAI;IACVrB,QAAQ,EAAE,QAAQ;IAClBvC,IAAI,EAAE,IAAI;IACV6D,OAAO,EAAE;EACX,CAAC,CACF;;EAED;EACA,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQ9D,QAAQ;MACd,KAAK,OAAO;QACV,OAAO,CACL;UAAEiD,KAAK,EAAE,eAAe;UAAEc,KAAK,EAAE,UAAU;UAAEC,MAAM,EAAE,QAAQ;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC7H;UAAElB,KAAK,EAAE,iBAAiB;UAAEc,KAAK,EAAE,IAAI;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EACrH;UAAElB,KAAK,EAAE,cAAc;UAAEc,KAAK,EAAE,IAAI;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAS,CAAC,EAC5G;UAAElB,KAAK,EAAE,qBAAqB;UAAEc,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE,KAAK;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAU,CAAC,CACvH;MACH,KAAK,OAAO;QACV,OAAO,CACL;UAAElB,KAAK,EAAE,iBAAiB;UAAEc,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EACpH;UAAElB,KAAK,EAAE,mBAAmB;UAAEc,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE,KAAK;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EACzH;UAAElB,KAAK,EAAE,eAAe;UAAEc,KAAK,EAAE,IAAI;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,aAAa;UAAEC,KAAK,EAAE;QAAU,CAAC,EACpH;UAAElB,KAAK,EAAE,sBAAsB;UAAEc,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE,KAAK;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,WAAW;UAAEC,KAAK,EAAE;QAAS,CAAC,CAC3H;MACH,KAAK,QAAQ;QACX,OAAO,CACL;UAAElB,KAAK,EAAE,UAAU;UAAEc,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,aAAa;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC9G;UAAElB,KAAK,EAAE,iBAAiB;UAAEc,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC/G;UAAElB,KAAK,EAAE,cAAc;UAAEc,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE,KAAK;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAS,CAAC,EAC9G;UAAElB,KAAK,EAAE,mBAAmB;UAAEc,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE,GAAG;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,CACrH;MACH,KAAK,QAAQ;QACX,OAAO,CACL;UAAElB,KAAK,EAAE,iBAAiB;UAAEc,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC7G;UAAElB,KAAK,EAAE,mBAAmB;UAAEc,KAAK,EAAE,IAAI;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAS,CAAC,EACpH;UAAElB,KAAK,EAAE,eAAe;UAAEc,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,EAChH;UAAElB,KAAK,EAAE,qBAAqB;UAAEc,KAAK,EAAE,IAAI;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,WAAW;UAAEC,KAAK,EAAE;QAAU,CAAC,CACzH;MACH;QACE,OAAO,EAAE;IACb;EACF,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGnC,YAAY,CAACoC,MAAM,CAAC1B,OAAO,IAAI;IACtD,MAAM2B,aAAa,GAAG3B,OAAO,CAACnB,IAAI,CAAC+C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChE,WAAW,CAAC+D,WAAW,CAAC,CAAC,CAAC,IAC/D5B,OAAO,CAACR,WAAW,CAACoC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChE,WAAW,CAAC+D,WAAW,CAAC,CAAC,CAAC;IAC1F,MAAME,aAAa,GAAG/D,WAAW,KAAK,KAAK,IACtBiC,OAAO,CAACP,MAAM,CAACmC,WAAW,CAAC,CAAC,KAAK7D,WAAW,CAAC6D,WAAW,CAAC,CAAC;IAC/E,OAAOD,aAAa,IAAIG,aAAa;EACvC,CAAC,CAAC;;EAEF;EACA,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IACpC1E,WAAW,CAAC0E,OAAO,CAAC;EACtB,CAAC;;EAED;EACA,IAAIrE,OAAO,EAAE;IACX,oBACEX,OAAA;MAAKiF,SAAS,EAAC,8GAA8G;MAAAC,QAAA,eAC3HlF,OAAA;QAAKiF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlF,OAAA;UAAKiF,SAAS,EAAC;QAA4E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClGtF,OAAA;UAAGiF,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAACjF,QAAQ,EAAE;IACb,oBACEL,OAAA;MAAKiF,SAAS,EAAC,8GAA8G;MAAAC,QAAA,eAC3HlF,OAAA;QAAKiF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlF,OAAA;UAAGiF,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAC;QAAsD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC7FtF,OAAA,CAACd,IAAI;UAACqG,EAAE,EAAC,QAAQ;UAACN,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEtF,OAAA;IAAKiF,SAAS,EAAC,6EAA6E;IAAAC,QAAA,gBAE1FlF,OAAA,CAACV,eAAe;MACde,QAAQ,EAAEA,QAAQ,CAACuE,WAAW,CAAC,CAAE;MACjCY,WAAW,EAAEA,WAAY;MACzBjF,mBAAmB,EAAEA;IAAoB;MAAA4E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAGFtF,OAAA;MAAKiF,SAAS,EAAC,iDAAiD;MAAAC,QAAA,eAC9DlF,OAAA;QAAKiF,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAClElF,OAAA;UAAKiF,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtClF,OAAA;YAAKiF,SAAS,EAAC;UAAiD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvEtF,OAAA;YAAMiF,SAAS,EAAC,oCAAoC;YAAAC,QAAA,GAAC,4BAA0B,EAAC7E,QAAQ;UAAA;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7F,CAAC,eACNtF,OAAA;UAAKiF,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EACrC,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAACO,GAAG,CAAE/D,IAAI,iBAC/C1B,OAAA;YAEE0F,OAAO,EAAEA,CAAA,KAAMX,gBAAgB,CAACrD,IAAI,CAAE;YACtCuD,SAAS,EAAE,wEACT5E,QAAQ,KAAKqB,IAAI,GACb,mCAAmC,GACnC,uDAAuD,EAC1D;YAAAwD,QAAA,EAEFxD;UAAI,GARAA,IAAI;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASH,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtF,OAAA,CAACT,eAAe;MACdc,QAAQ,EAAEA,QAAS;MACnBsF,cAAc,EAAE3E,cAAe;MAC/B4E,cAAc,EAAE9E,cAAe;MAC/BD,WAAW,EAAEA;IAAY;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC,eAGFtF,OAAA;MAAKiF,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBAEpClF,OAAA;QAAKiF,SAAS,EAAC,4DAA4D;QAAAC,QAAA,EACxEf,UAAU,CAAC,CAAC,CAACsB,GAAG,CAAC,CAACI,GAAG,EAAEC,KAAK,kBAC3B9F,OAAA;UAAiBiF,SAAS,EAAC,iBAAiB;UAACc,KAAK,EAAE;YAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;UAAI,CAAE;UAAAZ,QAAA,eACxFlF,OAAA,CAACR,OAAO;YACN8D,KAAK,EAAEuC,GAAG,CAACvC,KAAM;YACjBc,KAAK,EAAEyB,GAAG,CAACzB,KAAM;YACjBC,MAAM,EAAEwB,GAAG,CAACxB,MAAO;YACnBC,UAAU,EAAEuB,GAAG,CAACvB,UAAW;YAC3BC,IAAI,EAAEsB,GAAG,CAACtB,IAAK;YACfC,KAAK,EAAEqB,GAAG,CAACrB;UAAM;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC,GARMQ,KAAK;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNtF,OAAA;QAAKiF,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAE1DlF,OAAA;UAAKiF,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBAEtClF,OAAA;YAAKiF,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBlF,OAAA;cAAKiF,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDlF,OAAA;gBAAAkF,QAAA,gBACElF,OAAA;kBAAIiF,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,EACjE7E,QAAQ,KAAK,QAAQ,GAAG,oBAAoB,GAAG;gBAAa;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACLtF,OAAA;kBAAGiF,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAqC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACNtF,OAAA,CAACd,IAAI;gBACHqG,EAAE,EAAC,6BAA6B;gBAChCN,SAAS,EAAC,gKAAgK;gBAAAC,QAAA,GAC3K,gBAEC,eAAAlF,OAAA;kBAAKiF,SAAS,EAAC,SAAS;kBAACgB,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAjB,QAAA,eAC5ElF,OAAA;oBAAMoG,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAc;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENtF,OAAA;cAAKiF,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACnDT,gBAAgB,CAACgB,GAAG,CAAC,CAACzC,OAAO,EAAE8C,KAAK,kBACnC9F,OAAA;gBAAsBiF,SAAS,EAAC,iBAAiB;gBAACc,KAAK,EAAE;kBAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;gBAAI,CAAE;gBAAAZ,QAAA,eAC7FlF,OAAA,CAACP,WAAW;kBACVuD,OAAO,EAAEA,OAAQ;kBACjB3C,QAAQ,EAAEA;gBAAS;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB;cAAC,GAJMtC,OAAO,CAACT,EAAE;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAELb,gBAAgB,CAACjD,MAAM,KAAK,CAAC,iBAC5BxB,OAAA;cAAKiF,SAAS,EAAC,mFAAmF;cAAAC,QAAA,gBAChGlF,OAAA;gBAAKiF,SAAS,EAAC,mFAAmF;gBAAAC,QAAA,eAChGlF,OAAA;kBAAKiF,SAAS,EAAC,wBAAwB;kBAACgB,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAjB,QAAA,eAC3FlF,OAAA;oBAAMoG,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAsH;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3L;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtF,OAAA;gBAAGiF,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAsC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChFtF,OAAA;gBAAGiF,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAA4C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNtF,OAAA,CAACL,YAAY;YAACU,QAAQ,EAAEA;UAAS;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eAGNtF,OAAA;UAAKiF,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBlF,OAAA,CAACN,YAAY;YAAC8G,UAAU,EAAEzD,cAAe;YAAC1C,QAAQ,EAAEA;UAAS;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChEtF,OAAA,CAACF,iBAAiB;YAAC2G,aAAa,EAAE3C,iBAAkB;YAACzD,QAAQ,EAAEA;UAAS;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtF,OAAA;QAAKiF,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDlF,OAAA,CAACJ,WAAW;UAAC8G,KAAK,EAAErD,SAAU;UAAChD,QAAQ,EAAEA;QAAS;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDtF,OAAA,CAACH,YAAY;UAAC8G,WAAW,EAAEnD,eAAgB;UAACnD,QAAQ,EAAEA;QAAS;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpF,EAAA,CA9fID,kBAAkB;EAAA,QACLb,OAAO,EACPD,WAAW;AAAA;AAAAyH,EAAA,GAFxB3G,kBAAkB;AAggBxB,eAAeA,kBAAkB;AAAC,IAAA2G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}