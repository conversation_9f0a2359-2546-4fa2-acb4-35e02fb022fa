[{"D:\\PM\\agnoworksphere\\src\\index.jsx": "1", "D:\\PM\\agnoworksphere\\src\\App.jsx": "2", "D:\\PM\\agnoworksphere\\src\\Routes.jsx": "3", "D:\\PM\\agnoworksphere\\src\\contexts\\AuthContext.jsx": "4", "D:\\PM\\agnoworksphere\\src\\components\\ScrollToTop.jsx": "5", "D:\\PM\\agnoworksphere\\src\\components\\ErrorBoundary.jsx": "6", "D:\\PM\\agnoworksphere\\src\\pages\\NotFound.jsx": "7", "D:\\PM\\agnoworksphere\\src\\pages\\register\\index.jsx": "8", "D:\\PM\\agnoworksphere\\src\\pages\\login\\index.jsx": "9", "D:\\PM\\agnoworksphere\\src\\pages\\kanban-board\\index.jsx": "10", "D:\\PM\\agnoworksphere\\src\\pages\\card-details\\index.jsx": "11", "D:\\PM\\agnoworksphere\\src\\pages\\team-members\\index.jsx": "12", "D:\\PM\\agnoworksphere\\src\\pages\\organization-settings\\index.jsx": "13", "D:\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\index.jsx": "14", "D:\\PM\\agnoworksphere\\src\\components\\AppIcon.jsx": "15", "D:\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationHeader.jsx": "16", "D:\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationForm.jsx": "17", "D:\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginForm.jsx": "18", "D:\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginHeader.jsx": "19", "D:\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationBenefits.jsx": "20", "D:\\PM\\agnoworksphere\\src\\pages\\login\\components\\CredentialsHelper.jsx": "21", "D:\\PM\\agnoworksphere\\src\\pages\\login\\components\\SecurityBadges.jsx": "22", "D:\\PM\\agnoworksphere\\src\\components\\ui\\Button.jsx": "23", "D:\\PM\\agnoworksphere\\src\\components\\ui\\Select.jsx": "24", "D:\\PM\\agnoworksphere\\src\\components\\ui\\Header.jsx": "25", "D:\\PM\\agnoworksphere\\src\\components\\ui\\Input.jsx": "26", "D:\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddCardModal.jsx": "27", "D:\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardHeader.jsx": "28", "D:\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\InviteMemberModal.jsx": "29", "D:\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddColumnModal.jsx": "30", "D:\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardColumn.jsx": "31", "D:\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardHeader.jsx": "32", "D:\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ChecklistManager.jsx": "33", "D:\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\DueDatePicker.jsx": "34", "D:\\PM\\agnoworksphere\\src\\utils\\organizationService.js": "35", "D:\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ActivityTimeline.jsx": "36", "D:\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\MemberAssignment.jsx": "37", "D:\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardDescription.jsx": "38", "D:\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\LabelManager.jsx": "39", "D:\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\InviteMemberModal.jsx": "40", "D:\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberCard.jsx": "41", "D:\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberTable.jsx": "42", "D:\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberActivityModal.jsx": "43", "D:\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\RemoveMemberModal.jsx": "44", "D:\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\EditRoleModal.jsx": "45", "D:\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\BulkActionsBar.jsx": "46", "D:\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\StatsOverview.jsx": "47", "D:\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\IntegrationCard.jsx": "48", "D:\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\ActivityFeed.jsx": "49", "D:\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\NotificationCenter.jsx": "50", "D:\\PM\\agnoworksphere\\src\\utils\\cn.js": "51", "D:\\PM\\agnoworksphere\\src\\components\\ui\\Checkbox.jsx": "52", "D:\\PM\\agnoworksphere\\src\\components\\AppImage.jsx": "53", "D:\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\TaskCard.jsx": "54", "D:\\PM\\agnoworksphere\\src\\components\\ui\\Breadcrumb.jsx": "55", "D:\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\index.jsx": "56", "D:\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\PersonalInfoTab.jsx": "57", "D:\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\SecurityTab.jsx": "58", "D:\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\NotificationsTab.jsx": "59", "D:\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\GeneralSettings.jsx": "60", "D:\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\SecuritySettings.jsx": "61", "D:\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\IntegrationSettings.jsx": "62", "D:\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\MemberManagement.jsx": "63", "D:\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\BillingSettings.jsx": "64", "D:\\PM\\agnoworksphere\\src\\pages\\project-management\\index.jsx": "65", "D:\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\TasksTab.jsx": "66", "D:\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\ProjectOverview.jsx": "67", "D:\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\SettingsTab.jsx": "68", "D:\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\index.jsx": "69", "D:\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\KPICard.jsx": "70", "D:\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ActivityFeed.jsx": "71", "D:\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\DashboardHeader.jsx": "72", "D:\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ProjectCard.jsx": "73", "D:\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TaskSummary.jsx": "74", "D:\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\QuickActions.jsx": "75", "D:\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\NotificationPanel.jsx": "76", "D:\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TeamOverview.jsx": "77", "D:\\PM\\agnoworksphere\\src\\components\\ui\\RoleBasedHeader.jsx": "78", "D:\\PM\\agnoworksphere\\src\\utils\\authService.js": "79", "D:\\PM\\agnoworksphere\\src\\utils\\api.js": "80", "D:\\PM\\agnoworksphere\\src\\pages\\organization-setup\\index.jsx": "81", "D:\\PM\\agnoworksphere\\src\\utils\\roleBasedRouting.js": "82"}, {"size": 271, "mtime": *************, "results": "83", "hashOfConfig": "84"}, {"size": 271, "mtime": *************, "results": "85", "hashOfConfig": "84"}, {"size": 2070, "mtime": *************, "results": "86", "hashOfConfig": "84"}, {"size": 4183, "mtime": *************, "results": "87", "hashOfConfig": "84"}, {"size": 263, "mtime": *************, "results": "88", "hashOfConfig": "84"}, {"size": 2597, "mtime": *************, "results": "89", "hashOfConfig": "84"}, {"size": 1443, "mtime": *************, "results": "90", "hashOfConfig": "84"}, {"size": 1800, "mtime": *************, "results": "91", "hashOfConfig": "84"}, {"size": 1851, "mtime": *************, "results": "92", "hashOfConfig": "84"}, {"size": 15101, "mtime": *************, "results": "93", "hashOfConfig": "84"}, {"size": 7262, "mtime": *************, "results": "94", "hashOfConfig": "84"}, {"size": 14919, "mtime": *************, "results": "95", "hashOfConfig": "84"}, {"size": 6671, "mtime": *********6000, "results": "96", "hashOfConfig": "84"}, {"size": 11787, "mtime": *************, "results": "97", "hashOfConfig": "84"}, {"size": 619, "mtime": *************, "results": "98", "hashOfConfig": "84"}, {"size": 1270, "mtime": *************, "results": "99", "hashOfConfig": "84"}, {"size": 15038, "mtime": 1753955566310, "results": "100", "hashOfConfig": "84"}, {"size": 8638, "mtime": 1753954989118, "results": "101", "hashOfConfig": "84"}, {"size": 1661, "mtime": *************, "results": "102", "hashOfConfig": "84"}, {"size": 3061, "mtime": *************, "results": "103", "hashOfConfig": "84"}, {"size": 4355, "mtime": *************, "results": "104", "hashOfConfig": "84"}, {"size": 2069, "mtime": *************, "results": "105", "hashOfConfig": "84"}, {"size": 3229, "mtime": 1753931433455, "results": "106", "hashOfConfig": "84"}, {"size": 9775, "mtime": *************, "results": "107", "hashOfConfig": "84"}, {"size": 12210, "mtime": *************, "results": "108", "hashOfConfig": "84"}, {"size": 3119, "mtime": *************, "results": "109", "hashOfConfig": "84"}, {"size": 7619, "mtime": 1753932986546, "results": "110", "hashOfConfig": "84"}, {"size": 8654, "mtime": 1753904226824, "results": "111", "hashOfConfig": "84"}, {"size": 5902, "mtime": *************, "results": "112", "hashOfConfig": "84"}, {"size": 4319, "mtime": *************, "results": "113", "hashOfConfig": "84"}, {"size": 4222, "mtime": *************, "results": "114", "hashOfConfig": "84"}, {"size": 2962, "mtime": *************, "results": "115", "hashOfConfig": "84"}, {"size": 7240, "mtime": *************, "results": "116", "hashOfConfig": "84"}, {"size": 4192, "mtime": *************, "results": "117", "hashOfConfig": "84"}, {"size": 3289, "mtime": 1753948273057, "results": "118", "hashOfConfig": "84"}, {"size": 8238, "mtime": *************, "results": "119", "hashOfConfig": "84"}, {"size": 6960, "mtime": *************, "results": "120", "hashOfConfig": "84"}, {"size": 2269, "mtime": *************, "results": "121", "hashOfConfig": "84"}, {"size": 5011, "mtime": *************, "results": "122", "hashOfConfig": "84"}, {"size": 5667, "mtime": *************, "results": "123", "hashOfConfig": "84"}, {"size": 3454, "mtime": *************, "results": "124", "hashOfConfig": "84"}, {"size": 7881, "mtime": *************, "results": "125", "hashOfConfig": "84"}, {"size": 6935, "mtime": *************, "results": "126", "hashOfConfig": "84"}, {"size": 5068, "mtime": *************, "results": "127", "hashOfConfig": "84"}, {"size": 6178, "mtime": *************, "results": "128", "hashOfConfig": "84"}, {"size": 3299, "mtime": *************, "results": "129", "hashOfConfig": "84"}, {"size": 1689, "mtime": *************, "results": "130", "hashOfConfig": "84"}, {"size": 5556, "mtime": *************, "results": "131", "hashOfConfig": "84"}, {"size": 5370, "mtime": *************, "results": "132", "hashOfConfig": "84"}, {"size": 5637, "mtime": *************, "results": "133", "hashOfConfig": "84"}, {"size": 139, "mtime": *************, "results": "134", "hashOfConfig": "84"}, {"size": 4753, "mtime": *************, "results": "135", "hashOfConfig": "84"}, {"size": 329, "mtime": *************, "results": "136", "hashOfConfig": "84"}, {"size": 6088, "mtime": *************, "results": "137", "hashOfConfig": "84"}, {"size": 3004, "mtime": 1753902304157, "results": "138", "hashOfConfig": "84"}, {"size": 6894, "mtime": *********6000, "results": "139", "hashOfConfig": "84"}, {"size": 7859, "mtime": *********6000, "results": "140", "hashOfConfig": "84"}, {"size": 13656, "mtime": *********6000, "results": "141", "hashOfConfig": "84"}, {"size": 13718, "mtime": *********6000, "results": "142", "hashOfConfig": "84"}, {"size": 10393, "mtime": *********6000, "results": "143", "hashOfConfig": "84"}, {"size": 12779, "mtime": *********6000, "results": "144", "hashOfConfig": "84"}, {"size": 16373, "mtime": *********6000, "results": "145", "hashOfConfig": "84"}, {"size": 19344, "mtime": *********6000, "results": "146", "hashOfConfig": "84"}, {"size": 18772, "mtime": *********6000, "results": "147", "hashOfConfig": "84"}, {"size": 6799, "mtime": 1753930441637, "results": "148", "hashOfConfig": "84"}, {"size": 17413, "mtime": 1753667086000, "results": "149", "hashOfConfig": "84"}, {"size": 13209, "mtime": 1753667086000, "results": "150", "hashOfConfig": "84"}, {"size": 19464, "mtime": 1753667086000, "results": "151", "hashOfConfig": "84"}, {"size": 22120, "mtime": 1753955016831, "results": "152", "hashOfConfig": "84"}, {"size": 2946, "mtime": 1753916458000, "results": "153", "hashOfConfig": "84"}, {"size": 3744, "mtime": 1753916090000, "results": "154", "hashOfConfig": "84"}, {"size": 5117, "mtime": 1753916458000, "results": "155", "hashOfConfig": "84"}, {"size": 5879, "mtime": 1753916458000, "results": "156", "hashOfConfig": "84"}, {"size": 5802, "mtime": 1753916090000, "results": "157", "hashOfConfig": "84"}, {"size": 5232, "mtime": 1753916090000, "results": "158", "hashOfConfig": "84"}, {"size": 7859, "mtime": 1753916090000, "results": "159", "hashOfConfig": "84"}, {"size": 5794, "mtime": 1753916090000, "results": "160", "hashOfConfig": "84"}, {"size": 13322, "mtime": 1753939326386, "results": "161", "hashOfConfig": "84"}, {"size": 4514, "mtime": 1753949622452, "results": "162", "hashOfConfig": "84"}, {"size": 7253, "mtime": 1753949642619, "results": "163", "hashOfConfig": "84"}, {"size": 6367, "mtime": 1753954819658, "results": "164", "hashOfConfig": "84"}, {"size": 4133, "mtime": 1753954921887, "results": "165", "hashOfConfig": "84"}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1s6ngp", {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\PM\\agnoworksphere\\src\\index.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\App.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\Routes.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\contexts\\AuthContext.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\components\\ScrollToTop.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\components\\ErrorBoundary.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\NotFound.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\register\\index.jsx", ["412", "413", "414"], [], "D:\\PM\\agnoworksphere\\src\\pages\\login\\index.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\kanban-board\\index.jsx", ["415"], [], "D:\\PM\\agnoworksphere\\src\\pages\\card-details\\index.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\team-members\\index.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\organization-settings\\index.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\index.jsx", ["416"], [], "D:\\PM\\agnoworksphere\\src\\components\\AppIcon.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationHeader.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationForm.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginForm.jsx", ["417"], [], "D:\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginHeader.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationBenefits.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\login\\components\\CredentialsHelper.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\login\\components\\SecurityBadges.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\components\\ui\\Button.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\components\\ui\\Select.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\components\\ui\\Header.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\components\\ui\\Input.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddCardModal.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardHeader.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\InviteMemberModal.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddColumnModal.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardColumn.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardHeader.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ChecklistManager.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\DueDatePicker.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\utils\\organizationService.js", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ActivityTimeline.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\MemberAssignment.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardDescription.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\LabelManager.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\InviteMemberModal.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberCard.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberTable.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberActivityModal.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\RemoveMemberModal.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\EditRoleModal.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\BulkActionsBar.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\StatsOverview.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\IntegrationCard.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\ActivityFeed.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\NotificationCenter.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\utils\\cn.js", [], [], "D:\\PM\\agnoworksphere\\src\\components\\ui\\Checkbox.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\components\\AppImage.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\TaskCard.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\components\\ui\\Breadcrumb.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\index.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\PersonalInfoTab.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\SecurityTab.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\NotificationsTab.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\GeneralSettings.jsx", ["418"], [], "D:\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\SecuritySettings.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\IntegrationSettings.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\MemberManagement.jsx", ["419", "420"], [], "D:\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\BillingSettings.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\project-management\\index.jsx", ["421"], [], "D:\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\TasksTab.jsx", ["422"], [], "D:\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\ProjectOverview.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\SettingsTab.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\index.jsx", ["423"], [], "D:\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\KPICard.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ActivityFeed.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\DashboardHeader.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ProjectCard.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TaskSummary.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\QuickActions.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\NotificationPanel.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TeamOverview.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\components\\ui\\RoleBasedHeader.jsx", ["424"], [], "D:\\PM\\agnoworksphere\\src\\utils\\authService.js", [], [], "D:\\PM\\agnoworksphere\\src\\utils\\api.js", [], [], "D:\\PM\\agnoworksphere\\src\\pages\\organization-setup\\index.jsx", [], [], "D:\\PM\\agnoworksphere\\src\\utils\\roleBasedRouting.js", [], [], {"ruleId": "425", "severity": 1, "message": "426", "line": 30, "column": 15, "nodeType": "427", "endLine": 30, "endColumn": 81}, {"ruleId": "425", "severity": 1, "message": "426", "line": 33, "column": 15, "nodeType": "427", "endLine": 33, "endColumn": 81}, {"ruleId": "425", "severity": 1, "message": "426", "line": 36, "column": 15, "nodeType": "427", "endLine": 36, "endColumn": 81}, {"ruleId": "428", "severity": 1, "message": "429", "line": 1, "column": 27, "nodeType": "430", "messageId": "431", "endLine": 1, "endColumn": 36}, {"ruleId": "432", "severity": 1, "message": "433", "line": 33, "column": 6, "nodeType": "434", "endLine": 33, "endColumn": 25, "suggestions": "435"}, {"ruleId": "428", "severity": 1, "message": "436", "line": 21, "column": 9, "nodeType": "430", "messageId": "431", "endLine": 21, "endColumn": 24}, {"ruleId": "428", "severity": 1, "message": "437", "line": 20, "column": 10, "nodeType": "430", "messageId": "431", "endLine": 20, "endColumn": 18}, {"ruleId": "438", "severity": 1, "message": "439", "line": 158, "column": 5, "nodeType": "440", "messageId": "441", "endLine": 176, "endColumn": 6}, {"ruleId": "428", "severity": 1, "message": "442", "line": 188, "column": 9, "nodeType": "430", "messageId": "431", "endLine": 188, "endColumn": 21}, {"ruleId": "428", "severity": 1, "message": "443", "line": 13, "column": 27, "nodeType": "430", "messageId": "431", "endLine": 13, "endColumn": 45}, {"ruleId": "428", "severity": 1, "message": "444", "line": 129, "column": 9, "nodeType": "430", "messageId": "431", "endLine": 129, "endColumn": 24}, {"ruleId": "428", "severity": 1, "message": "445", "line": 20, "column": 10, "nodeType": "430", "messageId": "431", "endLine": 20, "endColumn": 23}, {"ruleId": "428", "severity": 1, "message": "446", "line": 94, "column": 9, "nodeType": "430", "messageId": "431", "endLine": 94, "endColumn": 21}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["447"], "'mockCredentials' is assigned a value but never used.", "'logoFile' is assigned a value but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'getRoleColor' is assigned a value but never used.", "'setSidebarExpanded' is assigned a value but never used.", "'priorityOptions' is assigned a value but never used.", "'organizations' is assigned a value but never used.", "'roleFeatures' is assigned a value but never used.", {"desc": "448", "fix": "449"}, "Update the dependencies array to be: [authLoading, loadDashboardData, user]", {"range": "450", "text": "451"}, [1289, 1308], "[authLoading, loadDashboardData, user]"]