{"ast": null, "code": "var _jsxFileName = \"D:\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\login\\\\components\\\\LoginForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../contexts/AuthContext';\nimport { determineUserRoute } from '../../../utils/roleBasedRouting';\nimport Button from '../../../components/ui/Button';\nimport Input from '../../../components/ui/Input';\nimport Icon from '../../../components/AppIcon';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginForm = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    login,\n    loading: authLoading,\n    error: authError,\n    clearError\n  } = useAuth();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [isLoading, setIsLoading] = useState(false);\n  const [showPassword, setShowPassword] = useState(false);\n\n  // Mock credentials for testing (can be removed in production)\n  const mockCredentials = [{\n    email: '<EMAIL>',\n    password: 'Owner123!',\n    role: 'Owner'\n  }, {\n    email: '<EMAIL>',\n    password: 'Admin123!',\n    role: 'Admin'\n  }, {\n    email: '<EMAIL>',\n    password: 'Member123!',\n    role: 'Member'\n  }, {\n    email: '<EMAIL>',\n    password: 'Viewer123!',\n    role: 'Viewer'\n  }];\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.email) {\n      newErrors.email = 'Email address is required';\n    } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n\n    // Clear auth error when user starts typing\n    if (authError) {\n      clearError();\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    setIsLoading(true);\n    setErrors({});\n    try {\n      console.log('🔍 Debug: Starting login process');\n      console.log('🔍 Debug: Form data:', {\n        email: formData.email,\n        password: '***'\n      });\n      console.log('🔍 Debug: Login function available:', typeof login);\n\n      // Use real authentication service\n      const result = await login(formData.email, formData.password);\n      console.log('🔍 Debug: Login result:', result);\n      if (result && result.success) {\n        console.log('🔍 Debug: Login successful, determining route...');\n\n        // Use utility function to determine appropriate route\n        try {\n          const route = await determineUserRoute(result.user);\n          console.log('🔍 Debug: Redirecting to:', route);\n          navigate(route);\n        } catch (routeError) {\n          console.error('🔍 Debug: Error determining route:', routeError);\n          // Fallback to role-based dashboard\n          navigate('/role-based-dashboard');\n        }\n      } else {\n        console.log('🔍 Debug: Login failed:', result === null || result === void 0 ? void 0 : result.error);\n        setErrors({\n          general: (result === null || result === void 0 ? void 0 : result.error) || 'Invalid email or password. Please check your credentials and try again.'\n        });\n      }\n    } catch (error) {\n      console.error('🔍 Debug: Login error:', error);\n      setErrors({\n        general: `An error occurred during login: ${error.message || 'Please try again.'}`\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full max-w-md mx-auto\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card border border-border rounded-lg shadow-enterprise p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-semibold text-text-primary mb-2\",\n          children: \"Welcome Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-text-secondary\",\n          children: \"Sign in to your Agno WorkSphere account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-6\",\n        children: [(errors.general || authError) && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-destructive/10 border border-destructive/20 rounded-md p-3 flex items-start space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            name: \"AlertCircle\",\n            size: 16,\n            className: \"text-destructive mt-0.5 flex-shrink-0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-destructive\",\n            children: errors.general || authError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Email Address\",\n            type: \"email\",\n            name: \"email\",\n            placeholder: \"Enter your email address\",\n            value: formData.email,\n            onChange: handleInputChange,\n            error: errors.email,\n            required: true,\n            disabled: isLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              label: \"Password\",\n              type: showPassword ? \"text\" : \"password\",\n              name: \"password\",\n              placeholder: \"Enter your password\",\n              value: formData.password,\n              onChange: handleInputChange,\n              error: errors.password,\n              required: true,\n              disabled: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: togglePasswordVisibility,\n              className: \"absolute right-3 top-9 text-text-secondary hover:text-text-primary transition-micro\",\n              disabled: isLoading,\n              children: /*#__PURE__*/_jsxDEV(Icon, {\n                name: showPassword ? \"EyeOff\" : \"Eye\",\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"flex items-center space-x-2 cursor-pointer\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              className: \"w-4 h-4 text-primary border-border rounded focus:ring-primary focus:ring-2\",\n              disabled: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-text-secondary\",\n              children: \"Remember me\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"text-sm text-primary hover:text-primary/80 transition-micro\",\n            disabled: isLoading,\n            children: \"Forgot password?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          variant: \"default\",\n          fullWidth: true,\n          loading: isLoading || authLoading,\n          disabled: isLoading || authLoading,\n          className: \"h-12\",\n          children: isLoading || authLoading ? 'Signing In...' : 'Sign In'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative my-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 flex items-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full border-t border-border\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative flex justify-center text-sm\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"px-2 bg-card text-text-secondary\",\n            children: \"Or continue with\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          disabled: isLoading,\n          className: \"h-10\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            name: \"Mail\",\n            size: 16,\n            className: \"mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), \"Google\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          disabled: isLoading,\n          className: \"h-10\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            name: \"Github\",\n            size: 16,\n            className: \"mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), \"GitHub\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mt-6\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-text-secondary\",\n          children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => navigate('/register'),\n            className: \"text-primary hover:text-primary/80 font-medium transition-micro\",\n            disabled: isLoading,\n            children: \"Create account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginForm, \"O6qSUj5AF1qkjgS/OWvT6F2C5yQ=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = LoginForm;\nexport default LoginForm;\nvar _c;\n$RefreshReg$(_c, \"LoginForm\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useAuth", "determineUserRoute", "<PERSON><PERSON>", "Input", "Icon", "jsxDEV", "_jsxDEV", "LoginForm", "_s", "navigate", "login", "loading", "authLoading", "error", "authError", "clearError", "formData", "setFormData", "email", "password", "errors", "setErrors", "isLoading", "setIsLoading", "showPassword", "setShowPassword", "mockCredentials", "role", "validateForm", "newErrors", "test", "length", "Object", "keys", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "console", "log", "result", "success", "route", "user", "routeError", "general", "message", "togglePasswordVisibility", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "size", "label", "type", "placeholder", "onChange", "required", "disabled", "onClick", "variant", "fullWidth", "_c", "$RefreshReg$"], "sources": ["D:/PM/agnoworksphere/src/pages/login/components/LoginForm.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../contexts/AuthContext';\nimport { determineUserRoute } from '../../../utils/roleBasedRouting';\nimport Button from '../../../components/ui/Button';\nimport Input from '../../../components/ui/Input';\nimport Icon from '../../../components/AppIcon';\n\nconst LoginForm = () => {\n  const navigate = useNavigate();\n  const { login, loading: authLoading, error: authError, clearError } = useAuth();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [isLoading, setIsLoading] = useState(false);\n  const [showPassword, setShowPassword] = useState(false);\n\n  // Mock credentials for testing (can be removed in production)\n  const mockCredentials = [\n    { email: '<EMAIL>', password: 'Owner123!', role: 'Owner' },\n    { email: '<EMAIL>', password: 'Admin123!', role: 'Admin' },\n    { email: '<EMAIL>', password: 'Member123!', role: 'Member' },\n    { email: '<EMAIL>', password: 'Viewer123!', role: 'Viewer' }\n  ];\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.email) {\n      newErrors.email = 'Email address is required';\n    } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n\n    // Clear auth error when user starts typing\n    if (authError) {\n      clearError();\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    setIsLoading(true);\n    setErrors({});\n\n    try {\n      console.log('🔍 Debug: Starting login process');\n      console.log('🔍 Debug: Form data:', { email: formData.email, password: '***' });\n      console.log('🔍 Debug: Login function available:', typeof login);\n\n      // Use real authentication service\n      const result = await login(formData.email, formData.password);\n\n      console.log('🔍 Debug: Login result:', result);\n\n      if (result && result.success) {\n        console.log('🔍 Debug: Login successful, determining route...');\n\n        // Use utility function to determine appropriate route\n        try {\n          const route = await determineUserRoute(result.user);\n          console.log('🔍 Debug: Redirecting to:', route);\n          navigate(route);\n        } catch (routeError) {\n          console.error('🔍 Debug: Error determining route:', routeError);\n          // Fallback to role-based dashboard\n          navigate('/role-based-dashboard');\n        }\n      } else {\n        console.log('🔍 Debug: Login failed:', result?.error);\n        setErrors({\n          general: result?.error || 'Invalid email or password. Please check your credentials and try again.'\n        });\n      }\n    } catch (error) {\n      console.error('🔍 Debug: Login error:', error);\n      setErrors({\n        general: `An error occurred during login: ${error.message || 'Please try again.'}`\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n\n  return (\n    <div className=\"w-full max-w-md mx-auto\">\n      <div className=\"bg-card border border-border rounded-lg shadow-enterprise p-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-2xl font-semibold text-text-primary mb-2\">\n            Welcome Back\n          </h1>\n          <p className=\"text-text-secondary\">\n            Sign in to your Agno WorkSphere account\n          </p>\n        </div>\n\n        {/* Login Form */}\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {/* General Error */}\n          {(errors.general || authError) && (\n            <div className=\"bg-destructive/10 border border-destructive/20 rounded-md p-3 flex items-start space-x-2\">\n              <Icon name=\"AlertCircle\" size={16} className=\"text-destructive mt-0.5 flex-shrink-0\" />\n              <p className=\"text-sm text-destructive\">{errors.general || authError}</p>\n            </div>\n          )}\n\n          {/* Email Field */}\n          <div>\n            <Input\n              label=\"Email Address\"\n              type=\"email\"\n              name=\"email\"\n              placeholder=\"Enter your email address\"\n              value={formData.email}\n              onChange={handleInputChange}\n              error={errors.email}\n              required\n              disabled={isLoading}\n            />\n          </div>\n\n          {/* Password Field */}\n          <div>\n            <div className=\"relative\">\n              <Input\n                label=\"Password\"\n                type={showPassword ? \"text\" : \"password\"}\n                name=\"password\"\n                placeholder=\"Enter your password\"\n                value={formData.password}\n                onChange={handleInputChange}\n                error={errors.password}\n                required\n                disabled={isLoading}\n              />\n              <button\n                type=\"button\"\n                onClick={togglePasswordVisibility}\n                className=\"absolute right-3 top-9 text-text-secondary hover:text-text-primary transition-micro\"\n                disabled={isLoading}\n              >\n                <Icon name={showPassword ? \"EyeOff\" : \"Eye\"} size={16} />\n              </button>\n            </div>\n          </div>\n\n          {/* Remember Me & Forgot Password */}\n          <div className=\"flex items-center justify-between\">\n            <label className=\"flex items-center space-x-2 cursor-pointer\">\n              <input\n                type=\"checkbox\"\n                className=\"w-4 h-4 text-primary border-border rounded focus:ring-primary focus:ring-2\"\n                disabled={isLoading}\n              />\n              <span className=\"text-sm text-text-secondary\">Remember me</span>\n            </label>\n            <button\n              type=\"button\"\n              className=\"text-sm text-primary hover:text-primary/80 transition-micro\"\n              disabled={isLoading}\n            >\n              Forgot password?\n            </button>\n          </div>\n\n          {/* Sign In Button */}\n          <Button\n            type=\"submit\"\n            variant=\"default\"\n            fullWidth\n            loading={isLoading || authLoading}\n            disabled={isLoading || authLoading}\n            className=\"h-12\"\n          >\n            {(isLoading || authLoading) ? 'Signing In...' : 'Sign In'}\n          </Button>\n        </form>\n\n        {/* Divider */}\n        <div className=\"relative my-6\">\n          <div className=\"absolute inset-0 flex items-center\">\n            <div className=\"w-full border-t border-border\"></div>\n          </div>\n          <div className=\"relative flex justify-center text-sm\">\n            <span className=\"px-2 bg-card text-text-secondary\">Or continue with</span>\n          </div>\n        </div>\n\n        {/* Social Login Options */}\n        <div className=\"grid grid-cols-2 gap-3\">\n          <Button\n            variant=\"outline\"\n            disabled={isLoading}\n            className=\"h-10\"\n          >\n            <Icon name=\"Mail\" size={16} className=\"mr-2\" />\n            Google\n          </Button>\n          <Button\n            variant=\"outline\"\n            disabled={isLoading}\n            className=\"h-10\"\n          >\n            <Icon name=\"Github\" size={16} className=\"mr-2\" />\n            GitHub\n          </Button>\n        </div>\n\n        {/* Sign Up Link */}\n        <div className=\"text-center mt-6\">\n          <p className=\"text-sm text-text-secondary\">\n            Don't have an account?{' '}\n            <button\n              type=\"button\"\n              onClick={() => navigate('/register')}\n              className=\"text-primary hover:text-primary/80 font-medium transition-micro\"\n              disabled={isLoading}\n            >\n              Create account\n            </button>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LoginForm;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,+BAA+B;AACvD,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,OAAOC,MAAM,MAAM,+BAA+B;AAClD,OAAOC,KAAK,MAAM,8BAA8B;AAChD,OAAOC,IAAI,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEW,KAAK;IAAEC,OAAO,EAAEC,WAAW;IAAEC,KAAK,EAAEC,SAAS;IAAEC;EAAW,CAAC,GAAGf,OAAO,CAAC,CAAC;EAC/E,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC;IACvCoB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM4B,eAAe,GAAG,CACtB;IAAER,KAAK,EAAE,gBAAgB;IAAEC,QAAQ,EAAE,WAAW;IAAEQ,IAAI,EAAE;EAAQ,CAAC,EACjE;IAAET,KAAK,EAAE,gBAAgB;IAAEC,QAAQ,EAAE,WAAW;IAAEQ,IAAI,EAAE;EAAQ,CAAC,EACjE;IAAET,KAAK,EAAE,iBAAiB;IAAEC,QAAQ,EAAE,YAAY;IAAEQ,IAAI,EAAE;EAAS,CAAC,EACpE;IAAET,KAAK,EAAE,iBAAiB;IAAEC,QAAQ,EAAE,YAAY;IAAEQ,IAAI,EAAE;EAAS,CAAC,CACrE;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACb,QAAQ,CAACE,KAAK,EAAE;MACnBW,SAAS,CAACX,KAAK,GAAG,2BAA2B;IAC/C,CAAC,MAAM,IAAI,CAAC,4BAA4B,CAACY,IAAI,CAACd,QAAQ,CAACE,KAAK,CAAC,EAAE;MAC7DW,SAAS,CAACX,KAAK,GAAG,oCAAoC;IACxD;IAEA,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE;MACtBU,SAAS,CAACV,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIH,QAAQ,CAACG,QAAQ,CAACY,MAAM,GAAG,CAAC,EAAE;MACvCF,SAAS,CAACV,QAAQ,GAAG,wCAAwC;IAC/D;IAEAE,SAAS,CAACQ,SAAS,CAAC;IACpB,OAAOG,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACE,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMG,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCrB,WAAW,CAACsB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIjB,MAAM,CAACgB,IAAI,CAAC,EAAE;MAChBf,SAAS,CAACkB,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACH,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,IAAItB,SAAS,EAAE;MACbC,UAAU,CAAC,CAAC;IACd;EACF,CAAC;EAED,MAAMyB,YAAY,GAAG,MAAOL,CAAC,IAAK;IAChCA,CAAC,CAACM,cAAc,CAAC,CAAC;IAElB,IAAI,CAACb,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEAL,YAAY,CAAC,IAAI,CAAC;IAClBF,SAAS,CAAC,CAAC,CAAC,CAAC;IAEb,IAAI;MACFqB,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAC/CD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;QAAEzB,KAAK,EAAEF,QAAQ,CAACE,KAAK;QAAEC,QAAQ,EAAE;MAAM,CAAC,CAAC;MAC/EuB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE,OAAOjC,KAAK,CAAC;;MAEhE;MACA,MAAMkC,MAAM,GAAG,MAAMlC,KAAK,CAACM,QAAQ,CAACE,KAAK,EAAEF,QAAQ,CAACG,QAAQ,CAAC;MAE7DuB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEC,MAAM,CAAC;MAE9C,IAAIA,MAAM,IAAIA,MAAM,CAACC,OAAO,EAAE;QAC5BH,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;;QAE/D;QACA,IAAI;UACF,MAAMG,KAAK,GAAG,MAAM7C,kBAAkB,CAAC2C,MAAM,CAACG,IAAI,CAAC;UACnDL,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEG,KAAK,CAAC;UAC/CrC,QAAQ,CAACqC,KAAK,CAAC;QACjB,CAAC,CAAC,OAAOE,UAAU,EAAE;UACnBN,OAAO,CAAC7B,KAAK,CAAC,oCAAoC,EAAEmC,UAAU,CAAC;UAC/D;UACAvC,QAAQ,CAAC,uBAAuB,CAAC;QACnC;MACF,CAAC,MAAM;QACLiC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE/B,KAAK,CAAC;QACrDQ,SAAS,CAAC;UACR4B,OAAO,EAAE,CAAAL,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE/B,KAAK,KAAI;QAC5B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd6B,OAAO,CAAC7B,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CQ,SAAS,CAAC;QACR4B,OAAO,EAAE,mCAAmCpC,KAAK,CAACqC,OAAO,IAAI,mBAAmB;MAClF,CAAC,CAAC;IACJ,CAAC,SAAS;MACR3B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM4B,wBAAwB,GAAGA,CAAA,KAAM;IACrC1B,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;EAED,oBACElB,OAAA;IAAK8C,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtC/C,OAAA;MAAK8C,SAAS,EAAC,+DAA+D;MAAAC,QAAA,gBAE5E/C,OAAA;QAAK8C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B/C,OAAA;UAAI8C,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAAC;QAE9D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLnD,OAAA;UAAG8C,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAC;QAEnC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNnD,OAAA;QAAMoD,QAAQ,EAAElB,YAAa;QAACY,SAAS,EAAC,WAAW;QAAAC,QAAA,GAEhD,CAACjC,MAAM,CAAC6B,OAAO,IAAInC,SAAS,kBAC3BR,OAAA;UAAK8C,SAAS,EAAC,0FAA0F;UAAAC,QAAA,gBACvG/C,OAAA,CAACF,IAAI;YAACgC,IAAI,EAAC,aAAa;YAACuB,IAAI,EAAE,EAAG;YAACP,SAAS,EAAC;UAAuC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvFnD,OAAA;YAAG8C,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAEjC,MAAM,CAAC6B,OAAO,IAAInC;UAAS;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CACN,eAGDnD,OAAA;UAAA+C,QAAA,eACE/C,OAAA,CAACH,KAAK;YACJyD,KAAK,EAAC,eAAe;YACrBC,IAAI,EAAC,OAAO;YACZzB,IAAI,EAAC,OAAO;YACZ0B,WAAW,EAAC,0BAA0B;YACtCzB,KAAK,EAAErB,QAAQ,CAACE,KAAM;YACtB6C,QAAQ,EAAE7B,iBAAkB;YAC5BrB,KAAK,EAAEO,MAAM,CAACF,KAAM;YACpB8C,QAAQ;YACRC,QAAQ,EAAE3C;UAAU;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNnD,OAAA;UAAA+C,QAAA,eACE/C,OAAA;YAAK8C,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB/C,OAAA,CAACH,KAAK;cACJyD,KAAK,EAAC,UAAU;cAChBC,IAAI,EAAErC,YAAY,GAAG,MAAM,GAAG,UAAW;cACzCY,IAAI,EAAC,UAAU;cACf0B,WAAW,EAAC,qBAAqB;cACjCzB,KAAK,EAAErB,QAAQ,CAACG,QAAS;cACzB4C,QAAQ,EAAE7B,iBAAkB;cAC5BrB,KAAK,EAAEO,MAAM,CAACD,QAAS;cACvB6C,QAAQ;cACRC,QAAQ,EAAE3C;YAAU;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACFnD,OAAA;cACEuD,IAAI,EAAC,QAAQ;cACbK,OAAO,EAAEf,wBAAyB;cAClCC,SAAS,EAAC,qFAAqF;cAC/Fa,QAAQ,EAAE3C,SAAU;cAAA+B,QAAA,eAEpB/C,OAAA,CAACF,IAAI;gBAACgC,IAAI,EAAEZ,YAAY,GAAG,QAAQ,GAAG,KAAM;gBAACmC,IAAI,EAAE;cAAG;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnD,OAAA;UAAK8C,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD/C,OAAA;YAAO8C,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBAC3D/C,OAAA;cACEuD,IAAI,EAAC,UAAU;cACfT,SAAS,EAAC,4EAA4E;cACtFa,QAAQ,EAAE3C;YAAU;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC,eACFnD,OAAA;cAAM8C,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACRnD,OAAA;YACEuD,IAAI,EAAC,QAAQ;YACbT,SAAS,EAAC,6DAA6D;YACvEa,QAAQ,EAAE3C,SAAU;YAAA+B,QAAA,EACrB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNnD,OAAA,CAACJ,MAAM;UACL2D,IAAI,EAAC,QAAQ;UACbM,OAAO,EAAC,SAAS;UACjBC,SAAS;UACTzD,OAAO,EAAEW,SAAS,IAAIV,WAAY;UAClCqD,QAAQ,EAAE3C,SAAS,IAAIV,WAAY;UACnCwC,SAAS,EAAC,MAAM;UAAAC,QAAA,EAEd/B,SAAS,IAAIV,WAAW,GAAI,eAAe,GAAG;QAAS;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGPnD,OAAA;QAAK8C,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B/C,OAAA;UAAK8C,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjD/C,OAAA;YAAK8C,SAAS,EAAC;UAA+B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACNnD,OAAA;UAAK8C,SAAS,EAAC,sCAAsC;UAAAC,QAAA,eACnD/C,OAAA;YAAM8C,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnD,OAAA;QAAK8C,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrC/C,OAAA,CAACJ,MAAM;UACLiE,OAAO,EAAC,SAAS;UACjBF,QAAQ,EAAE3C,SAAU;UACpB8B,SAAS,EAAC,MAAM;UAAAC,QAAA,gBAEhB/C,OAAA,CAACF,IAAI;YAACgC,IAAI,EAAC,MAAM;YAACuB,IAAI,EAAE,EAAG;YAACP,SAAS,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAEjD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnD,OAAA,CAACJ,MAAM;UACLiE,OAAO,EAAC,SAAS;UACjBF,QAAQ,EAAE3C,SAAU;UACpB8B,SAAS,EAAC,MAAM;UAAAC,QAAA,gBAEhB/C,OAAA,CAACF,IAAI;YAACgC,IAAI,EAAC,QAAQ;YAACuB,IAAI,EAAE,EAAG;YAACP,SAAS,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,UAEnD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNnD,OAAA;QAAK8C,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/B/C,OAAA;UAAG8C,SAAS,EAAC,6BAA6B;UAAAC,QAAA,GAAC,wBACnB,EAAC,GAAG,eAC1B/C,OAAA;YACEuD,IAAI,EAAC,QAAQ;YACbK,OAAO,EAAEA,CAAA,KAAMzD,QAAQ,CAAC,WAAW,CAAE;YACrC2C,SAAS,EAAC,iEAAiE;YAC3Ea,QAAQ,EAAE3C,SAAU;YAAA+B,QAAA,EACrB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjD,EAAA,CA9PID,SAAS;EAAA,QACIR,WAAW,EAC0CC,OAAO;AAAA;AAAAqE,EAAA,GAFzE9D,SAAS;AAgQf,eAAeA,SAAS;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}