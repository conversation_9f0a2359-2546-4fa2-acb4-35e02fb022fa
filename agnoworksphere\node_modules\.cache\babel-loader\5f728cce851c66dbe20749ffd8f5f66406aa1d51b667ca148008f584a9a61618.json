{"ast": null, "code": "// src/utils/roleBasedRouting.js\n\nimport { getOrganizations } from './organizationService';\n\n/**\n * Determines the appropriate route for a user based on their role\n * @param {Object} user - The authenticated user object\n * @returns {Promise<string>} - The route path to redirect to\n */\nexport const determineUserRoute = async user => {\n  if (!user) {\n    return '/login';\n  }\n  try {\n    // Get user's organizations\n    const orgsResult = await getOrganizations();\n    if (!orgsResult.data || orgsResult.data.length === 0) {\n      // User has no organizations - redirect to setup\n      return '/organization-setup';\n    }\n\n    // Get primary organization (first one)\n    const primaryOrg = orgsResult.data[0];\n    const userRole = primaryOrg.role;\n\n    // Route based on role\n    switch (userRole) {\n      case 'owner':\n      case 'admin':\n        return '/role-based-dashboard';\n      case 'member':\n      case 'viewer':\n        return '/kanban-board';\n      default:\n        return '/role-based-dashboard';\n    }\n  } catch (error) {\n    console.error('Error determining user route:', error);\n    // Fallback to role-based dashboard\n    return '/role-based-dashboard';\n  }\n};\n\n/**\n * Gets the user's role in their primary organization\n * @param {Object} user - The authenticated user object\n * @returns {Promise<Object>} - Object containing role and organization info\n */\nexport const getUserRoleInfo = async user => {\n  if (!user) {\n    return {\n      role: null,\n      organization: null,\n      organizations: []\n    };\n  }\n  try {\n    const orgsResult = await getOrganizations();\n    if (!orgsResult.data || orgsResult.data.length === 0) {\n      return {\n        role: null,\n        organization: null,\n        organizations: []\n      };\n    }\n    const primaryOrg = orgsResult.data[0];\n    return {\n      role: primaryOrg.role,\n      organization: primaryOrg,\n      organizations: orgsResult.data\n    };\n  } catch (error) {\n    console.error('Error getting user role info:', error);\n    return {\n      role: null,\n      organization: null,\n      organizations: []\n    };\n  }\n};\n\n/**\n * Checks if a user has permission to access a specific route\n * @param {string} userRole - The user's role\n * @param {string} route - The route to check\n * @returns {boolean} - Whether the user can access the route\n */\nexport const canAccessRoute = (userRole, route) => {\n  const rolePermissions = {\n    owner: ['/role-based-dashboard', '/organization-settings', '/team-members', '/kanban-board', '/project-management', '/user-profile-settings'],\n    admin: ['/role-based-dashboard', '/organization-settings', '/team-members', '/kanban-board', '/project-management', '/user-profile-settings'],\n    member: ['/kanban-board', '/team-members', '/project-management', '/user-profile-settings', '/role-based-dashboard'],\n    viewer: ['/kanban-board', '/team-members', '/user-profile-settings', '/role-based-dashboard']\n  };\n  const allowedRoutes = rolePermissions[userRole] || [];\n  return allowedRoutes.includes(route);\n};\n\n/**\n * Gets the default route for a specific role\n * @param {string} role - The user's role\n * @returns {string} - The default route for the role\n */\nexport const getDefaultRouteForRole = role => {\n  switch (role) {\n    case 'owner':\n    case 'admin':\n      return '/role-based-dashboard';\n    case 'member':\n    case 'viewer':\n      return '/kanban-board';\n    default:\n      return '/role-based-dashboard';\n  }\n};\n\n/**\n * Role hierarchy for permission checking\n */\nexport const ROLE_HIERARCHY = {\n  owner: 4,\n  admin: 3,\n  member: 2,\n  viewer: 1\n};\n\n/**\n * Checks if a user role has higher or equal permissions than required role\n * @param {string} userRole - The user's role\n * @param {string} requiredRole - The minimum required role\n * @returns {boolean} - Whether the user has sufficient permissions\n */\nexport const hasRolePermission = (userRole, requiredRole) => {\n  const userLevel = ROLE_HIERARCHY[userRole] || 0;\n  const requiredLevel = ROLE_HIERARCHY[requiredRole] || 0;\n  return userLevel >= requiredLevel;\n};", "map": {"version": 3, "names": ["getOrganizations", "determineUserRoute", "user", "orgsResult", "data", "length", "primaryOrg", "userRole", "role", "error", "console", "getUserRoleInfo", "organization", "organizations", "canAccessRoute", "route", "rolePermissions", "owner", "admin", "member", "viewer", "allowedRoutes", "includes", "getDefaultRouteForRole", "ROLE_HIERARCHY", "hasRolePermission", "requiredRole", "userLevel", "requiredLevel"], "sources": ["D:/PM/agnoworksphere/src/utils/roleBasedRouting.js"], "sourcesContent": ["// src/utils/roleBasedRouting.js\n\nimport { getOrganizations } from './organizationService';\n\n/**\n * Determines the appropriate route for a user based on their role\n * @param {Object} user - The authenticated user object\n * @returns {Promise<string>} - The route path to redirect to\n */\nexport const determineUserRoute = async (user) => {\n  if (!user) {\n    return '/login';\n  }\n\n  try {\n    // Get user's organizations\n    const orgsResult = await getOrganizations();\n    \n    if (!orgsResult.data || orgsResult.data.length === 0) {\n      // User has no organizations - redirect to setup\n      return '/organization-setup';\n    }\n\n    // Get primary organization (first one)\n    const primaryOrg = orgsResult.data[0];\n    const userRole = primaryOrg.role;\n\n    // Route based on role\n    switch (userRole) {\n      case 'owner':\n      case 'admin':\n        return '/role-based-dashboard';\n      case 'member':\n      case 'viewer':\n        return '/kanban-board';\n      default:\n        return '/role-based-dashboard';\n    }\n  } catch (error) {\n    console.error('Error determining user route:', error);\n    // Fallback to role-based dashboard\n    return '/role-based-dashboard';\n  }\n};\n\n/**\n * Gets the user's role in their primary organization\n * @param {Object} user - The authenticated user object\n * @returns {Promise<Object>} - Object containing role and organization info\n */\nexport const getUserRoleInfo = async (user) => {\n  if (!user) {\n    return { role: null, organization: null, organizations: [] };\n  }\n\n  try {\n    const orgsResult = await getOrganizations();\n    \n    if (!orgsResult.data || orgsResult.data.length === 0) {\n      return { role: null, organization: null, organizations: [] };\n    }\n\n    const primaryOrg = orgsResult.data[0];\n    \n    return {\n      role: primaryOrg.role,\n      organization: primaryOrg,\n      organizations: orgsResult.data\n    };\n  } catch (error) {\n    console.error('Error getting user role info:', error);\n    return { role: null, organization: null, organizations: [] };\n  }\n};\n\n/**\n * Checks if a user has permission to access a specific route\n * @param {string} userRole - The user's role\n * @param {string} route - The route to check\n * @returns {boolean} - Whether the user can access the route\n */\nexport const canAccessRoute = (userRole, route) => {\n  const rolePermissions = {\n    owner: [\n      '/role-based-dashboard',\n      '/organization-settings',\n      '/team-members',\n      '/kanban-board',\n      '/project-management',\n      '/user-profile-settings'\n    ],\n    admin: [\n      '/role-based-dashboard',\n      '/organization-settings',\n      '/team-members',\n      '/kanban-board',\n      '/project-management',\n      '/user-profile-settings'\n    ],\n    member: [\n      '/kanban-board',\n      '/team-members',\n      '/project-management',\n      '/user-profile-settings',\n      '/role-based-dashboard'\n    ],\n    viewer: [\n      '/kanban-board',\n      '/team-members',\n      '/user-profile-settings',\n      '/role-based-dashboard'\n    ]\n  };\n\n  const allowedRoutes = rolePermissions[userRole] || [];\n  return allowedRoutes.includes(route);\n};\n\n/**\n * Gets the default route for a specific role\n * @param {string} role - The user's role\n * @returns {string} - The default route for the role\n */\nexport const getDefaultRouteForRole = (role) => {\n  switch (role) {\n    case 'owner':\n    case 'admin':\n      return '/role-based-dashboard';\n    case 'member':\n    case 'viewer':\n      return '/kanban-board';\n    default:\n      return '/role-based-dashboard';\n  }\n};\n\n/**\n * Role hierarchy for permission checking\n */\nexport const ROLE_HIERARCHY = {\n  owner: 4,\n  admin: 3,\n  member: 2,\n  viewer: 1\n};\n\n/**\n * Checks if a user role has higher or equal permissions than required role\n * @param {string} userRole - The user's role\n * @param {string} requiredRole - The minimum required role\n * @returns {boolean} - Whether the user has sufficient permissions\n */\nexport const hasRolePermission = (userRole, requiredRole) => {\n  const userLevel = ROLE_HIERARCHY[userRole] || 0;\n  const requiredLevel = ROLE_HIERARCHY[requiredRole] || 0;\n  return userLevel >= requiredLevel;\n};\n"], "mappings": "AAAA;;AAEA,SAASA,gBAAgB,QAAQ,uBAAuB;;AAExD;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,kBAAkB,GAAG,MAAOC,IAAI,IAAK;EAChD,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,QAAQ;EACjB;EAEA,IAAI;IACF;IACA,MAAMC,UAAU,GAAG,MAAMH,gBAAgB,CAAC,CAAC;IAE3C,IAAI,CAACG,UAAU,CAACC,IAAI,IAAID,UAAU,CAACC,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;MACpD;MACA,OAAO,qBAAqB;IAC9B;;IAEA;IACA,MAAMC,UAAU,GAAGH,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC;IACrC,MAAMG,QAAQ,GAAGD,UAAU,CAACE,IAAI;;IAEhC;IACA,QAAQD,QAAQ;MACd,KAAK,OAAO;MACZ,KAAK,OAAO;QACV,OAAO,uBAAuB;MAChC,KAAK,QAAQ;MACb,KAAK,QAAQ;QACX,OAAO,eAAe;MACxB;QACE,OAAO,uBAAuB;IAClC;EACF,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACrD;IACA,OAAO,uBAAuB;EAChC;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,eAAe,GAAG,MAAOT,IAAI,IAAK;EAC7C,IAAI,CAACA,IAAI,EAAE;IACT,OAAO;MAAEM,IAAI,EAAE,IAAI;MAAEI,YAAY,EAAE,IAAI;MAAEC,aAAa,EAAE;IAAG,CAAC;EAC9D;EAEA,IAAI;IACF,MAAMV,UAAU,GAAG,MAAMH,gBAAgB,CAAC,CAAC;IAE3C,IAAI,CAACG,UAAU,CAACC,IAAI,IAAID,UAAU,CAACC,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;MACpD,OAAO;QAAEG,IAAI,EAAE,IAAI;QAAEI,YAAY,EAAE,IAAI;QAAEC,aAAa,EAAE;MAAG,CAAC;IAC9D;IAEA,MAAMP,UAAU,GAAGH,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC;IAErC,OAAO;MACLI,IAAI,EAAEF,UAAU,CAACE,IAAI;MACrBI,YAAY,EAAEN,UAAU;MACxBO,aAAa,EAAEV,UAAU,CAACC;IAC5B,CAAC;EACH,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACrD,OAAO;MAAED,IAAI,EAAE,IAAI;MAAEI,YAAY,EAAE,IAAI;MAAEC,aAAa,EAAE;IAAG,CAAC;EAC9D;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,cAAc,GAAGA,CAACP,QAAQ,EAAEQ,KAAK,KAAK;EACjD,MAAMC,eAAe,GAAG;IACtBC,KAAK,EAAE,CACL,uBAAuB,EACvB,wBAAwB,EACxB,eAAe,EACf,eAAe,EACf,qBAAqB,EACrB,wBAAwB,CACzB;IACDC,KAAK,EAAE,CACL,uBAAuB,EACvB,wBAAwB,EACxB,eAAe,EACf,eAAe,EACf,qBAAqB,EACrB,wBAAwB,CACzB;IACDC,MAAM,EAAE,CACN,eAAe,EACf,eAAe,EACf,qBAAqB,EACrB,wBAAwB,EACxB,uBAAuB,CACxB;IACDC,MAAM,EAAE,CACN,eAAe,EACf,eAAe,EACf,wBAAwB,EACxB,uBAAuB;EAE3B,CAAC;EAED,MAAMC,aAAa,GAAGL,eAAe,CAACT,QAAQ,CAAC,IAAI,EAAE;EACrD,OAAOc,aAAa,CAACC,QAAQ,CAACP,KAAK,CAAC;AACtC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMQ,sBAAsB,GAAIf,IAAI,IAAK;EAC9C,QAAQA,IAAI;IACV,KAAK,OAAO;IACZ,KAAK,OAAO;MACV,OAAO,uBAAuB;IAChC,KAAK,QAAQ;IACb,KAAK,QAAQ;MACX,OAAO,eAAe;IACxB;MACE,OAAO,uBAAuB;EAClC;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMgB,cAAc,GAAG;EAC5BP,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE;AACV,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,iBAAiB,GAAGA,CAAClB,QAAQ,EAAEmB,YAAY,KAAK;EAC3D,MAAMC,SAAS,GAAGH,cAAc,CAACjB,QAAQ,CAAC,IAAI,CAAC;EAC/C,MAAMqB,aAAa,GAAGJ,cAAc,CAACE,YAAY,CAAC,IAAI,CAAC;EACvD,OAAOC,SAAS,IAAIC,aAAa;AACnC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}