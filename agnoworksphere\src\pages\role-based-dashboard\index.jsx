import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { getUserRoleInfo } from '../../utils/roleBasedRouting';
import RoleBasedHeader from '../../components/ui/RoleBasedHeader';
import DashboardHeader from './components/DashboardHeader';
import KPICard from './components/KPICard';
import ProjectCard from './components/ProjectCard';
import ActivityFeed from './components/ActivityFeed';
import QuickActions from './components/QuickActions';
import TaskSummary from './components/TaskSummary';
import TeamOverview from './components/TeamOverview';
import NotificationPanel from './components/NotificationPanel';

const RoleBasedDashboard = () => {
  const { user } = useAuth();
  const location = useLocation();
  const [userRole, setUserRole] = useState(null);
  const [currentOrganization, setCurrentOrganization] = useState(null);
  const [organizations, setOrganizations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchValue, setSearchValue] = useState('');
  const [filterValue, setFilterValue] = useState('all');
  const [welcomeMessage, setWelcomeMessage] = useState(null);

  // Fetch user organizations and role on component mount
  useEffect(() => {
    const fetchUserData = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Get user's role and organization info
        const roleInfo = await getUserRoleInfo(user);

        if (roleInfo.role && roleInfo.organization) {
          setOrganizations(roleInfo.organizations);
          setCurrentOrganization(roleInfo.organization);
          setUserRole(roleInfo.role);

          console.log('🔍 Dashboard: Loaded user role:', roleInfo.role, 'for org:', roleInfo.organization.name);
        } else {
          console.warn('🔍 Dashboard: No organizations found for user');
          setUserRole('member'); // Default fallback
        }

        // Check for welcome message from registration
        if (location.state?.message && location.state?.isNewUser) {
          setWelcomeMessage({
            text: location.state.message,
            type: location.state.type || 'success'
          });

          // Clear the message after showing it
          setTimeout(() => setWelcomeMessage(null), 5000);
        }

      } catch (error) {
        console.error('🔍 Dashboard: Error fetching user data:', error);
        setUserRole('member'); // Default fallback
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [user, location.state]);

  // Mock data for different dashboard components
  const mockProjects = [
    {
      id: 1,
      name: "E-commerce Platform Redesign",
      description: "Complete overhaul of the existing e-commerce platform with modern UI/UX design and improved performance optimization.",
      status: "Active",
      priority: "High",
      progress: 75,
      dueDate: "Dec 15, 2025",
      team: [
        { name: "Sarah Johnson", avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150" },
        { name: "Mike Chen", avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150" },
        { name: "Emily Davis", avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150" },
        { name: "Alex Rodriguez", avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150" }
      ]
    },
    {
      id: 2,
      name: "Mobile App Development",
      description: "Native iOS and Android application development for customer engagement and loyalty program management.",
      status: "Active",
      priority: "Medium",
      progress: 45,
      dueDate: "Jan 30, 2026",
      team: [
        { name: "David Kim", avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150" },
        { name: "Lisa Wang", avatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150" },
        { name: "Tom Wilson", avatar: "https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150" }
      ]
    },
    {
      id: 3,
      name: "Data Analytics Dashboard",
      description: "Business intelligence dashboard for real-time analytics and reporting with advanced data visualization capabilities.",
      status: "Completed",
      priority: "Low",
      progress: 100,
      dueDate: "Nov 20, 2025",
      team: [
        { name: "Rachel Green", avatar: "https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150" },
        { name: "James Brown", avatar: "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150" }
      ]
    }
  ];

  const mockActivities = [
    {
      id: 1,
      type: "task_completed",
      user: { name: "Sarah Johnson", avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150" },
      description: "Completed the user interface mockups for the checkout process",
      project: "E-commerce Platform",
      timestamp: new Date(Date.now() - 15 * 60 * 1000),
      isPublic: true
    },
    {
      id: 2,
      type: "comment_added",
      user: { name: "Mike Chen", avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150" },
      description: "Added feedback on the mobile responsive design implementation",
      project: "E-commerce Platform",
      timestamp: new Date(Date.now() - 45 * 60 * 1000),
      isPublic: true
    },
    {
      id: 3,
      type: "project_created",
      user: { name: "Emily Davis", avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150" },
      description: "Created new project for Q1 marketing campaign automation",
      project: "Marketing Automation",
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      isPublic: false
    },
    {
      id: 4,
      type: "member_added",
      user: { name: "Alex Rodriguez", avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150" },
      description: "Joined the mobile app development team as a senior developer",
      project: "Mobile App Development",
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
      isPublic: true
    }
  ];

  const mockTasks = [
    {
      id: 1,
      title: "Implement payment gateway integration",
      status: "In Progress",
      priority: "High",
      dueDate: "Dec 10, 2025",
      assignee: "Sarah Johnson",
      project: "E-commerce Platform"
    },
    {
      id: 2,
      title: "Design user onboarding flow",
      status: "To Do",
      priority: "Medium",
      dueDate: "Dec 12, 2025",
      assignee: "Mike Chen",
      project: "Mobile App"
    },
    {
      id: 3,
      title: "Set up automated testing pipeline",
      status: "Review",
      priority: "High",
      dueDate: "Dec 8, 2025",
      assignee: "Emily Davis",
      project: "E-commerce Platform"
    },
    {
      id: 4,
      title: "Create API documentation",
      status: "Done",
      priority: "Low",
      dueDate: "Dec 5, 2025",
      assignee: "Alex Rodriguez",
      project: "Data Analytics"
    },
    {
      id: 5,
      title: "Optimize database queries",
      status: "Blocked",
      priority: "High",
      dueDate: "Dec 15, 2025",
      assignee: "David Kim",
      project: "E-commerce Platform"
    }
  ];

  const mockTeamMembers = [
    {
      id: 1,
      name: "Sarah Johnson",
      email: "<EMAIL>",
      role: "Admin",
      status: "Online",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150",
      department: "Design",
      currentTask: "UI/UX Design Review",
      tasksCompleted: 24,
      lastActive: new Date()
    },
    {
      id: 2,
      name: "Mike Chen",
      email: "<EMAIL>",
      role: "Member",
      status: "Away",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150",
      department: "Development",
      currentTask: "Frontend Implementation",
      tasksCompleted: 18,
      lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000)
    },
    {
      id: 3,
      name: "Emily Davis",
      email: "<EMAIL>",
      role: "Owner",
      status: "Online",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150",
      department: "Management",
      currentTask: "Project Planning",
      tasksCompleted: 32,
      lastActive: new Date()
    },
    {
      id: 4,
      name: "Alex Rodriguez",
      email: "<EMAIL>",
      role: "Member",
      status: "Busy",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150",
      department: "Development",
      currentTask: "API Integration",
      tasksCompleted: 15,
      lastActive: new Date(Date.now() - 30 * 60 * 1000)
    },
    {
      id: 5,
      name: "David Kim",
      email: "<EMAIL>",
      role: "Viewer",
      status: "Offline",
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150",
      department: "QA",
      currentTask: null,
      tasksCompleted: 8,
      lastActive: new Date(Date.now() - 24 * 60 * 60 * 1000)
    }
  ];

  const mockNotifications = [
    {
      id: 1,
      type: "task_assigned",
      title: "New Task Assigned",
      message: "You have been assigned to work on the payment gateway integration for the e-commerce platform.",
      timestamp: new Date(Date.now() - 30 * 60 * 1000),
      read: false,
      priority: "high",
      user: { name: "Emily Davis", avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150" },
      actions: [
        { label: "View Task", variant: "default" },
        { label: "Accept", variant: "outline" }
      ]
    },
    {
      id: 2,
      type: "deadline_reminder",
      title: "Deadline Approaching",
      message: "The UI/UX design review is due in 2 days. Please ensure all deliverables are ready for submission.",
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      read: false,
      priority: "medium",
      user: null,
      actions: [
        { label: "View Details", variant: "outline" }
      ]
    },
    {
      id: 3,
      type: "comment_mention",
      title: "You were mentioned",
      message: "Sarah Johnson mentioned you in a comment on the mobile app development project.",
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
      read: true,
      priority: "low",
      user: { name: "Sarah Johnson", avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150" },
      actions: [
        { label: "Reply", variant: "outline" }
      ]
    },
    {
      id: 4,
      type: "system_alert",
      title: "System Maintenance",
      message: "Scheduled maintenance will occur tonight from 11 PM to 1 AM EST. Some features may be temporarily unavailable.",
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
      read: true,
      priority: "medium",
      user: null,
      actions: []
    }
  ];

  // Role-based KPI data
  const getKPIData = () => {
    switch (userRole) {
      case 'Owner':
        return [
          { title: "Total Revenue", value: "$124,500", change: "+12.5%", changeType: "positive", icon: "DollarSign", color: "success" },
          { title: "Active Projects", value: "12", change: "+3", changeType: "positive", icon: "FolderOpen", color: "primary" },
          { title: "Team Members", value: "24", change: "+2", changeType: "positive", icon: "Users", color: "accent" },
          { title: "Client Satisfaction", value: "94%", change: "+2%", changeType: "positive", icon: "Heart", color: "success" }
        ];
      case 'Admin':
        return [
          { title: "Active Projects", value: "8", change: "+2", changeType: "positive", icon: "FolderOpen", color: "primary" },
          { title: "Team Productivity", value: "87%", change: "+5%", changeType: "positive", icon: "TrendingUp", color: "success" },
          { title: "Pending Tasks", value: "23", change: "-4", changeType: "positive", icon: "CheckSquare", color: "warning" },
          { title: "Resource Utilization", value: "92%", change: "+3%", changeType: "positive", icon: "BarChart3", color: "accent" }
        ];
      case 'Member':
        return [
          { title: "My Tasks", value: "8", change: "+2", changeType: "positive", icon: "CheckSquare", color: "primary" },
          { title: "Completed Today", value: "3", change: "+1", changeType: "positive", icon: "Check", color: "success" },
          { title: "Hours Logged", value: "32h", change: "+4h", changeType: "positive", icon: "Clock", color: "accent" },
          { title: "Projects Involved", value: "4", change: "0", changeType: "neutral", icon: "FolderOpen", color: "warning" }
        ];
      case 'Viewer':
        return [
          { title: "Projects Viewed", value: "6", change: "+1", changeType: "positive", icon: "Eye", color: "primary" },
          { title: "Reports Generated", value: "12", change: "+3", changeType: "positive", icon: "FileText", color: "accent" },
          { title: "Data Exported", value: "8", change: "+2", changeType: "positive", icon: "Download", color: "success" },
          { title: "Dashboards Accessed", value: "15", change: "+5", changeType: "positive", icon: "BarChart3", color: "warning" }
        ];
      default:
        return [];
    }
  };

  // Filter projects based on search and filter values
  const filteredProjects = mockProjects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchValue.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchValue.toLowerCase());
    const matchesFilter = filterValue === 'all' || 
                         project.status.toLowerCase() === filterValue.toLowerCase();
    return matchesSearch && matchesFilter;
  });

  // Role switcher for demo purposes
  const handleRoleChange = (newRole) => {
    setUserRole(newRole);
  };

  // Show loading state while fetching user data
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-slate-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  // Show message if no role is determined
  if (!userRole) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center">
        <div className="text-center">
          <p className="text-slate-600 mb-4">Unable to determine your role. Please contact support.</p>
          <Link to="/login" className="text-primary hover:text-primary/80">Return to Login</Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20">
      {/* Role-Based Header */}
      <RoleBasedHeader
        userRole={userRole.toLowerCase()}
        currentUser={user}
        currentOrganization={currentOrganization}
      />

      {/* Welcome Message for New Users */}
      {welcomeMessage && (
        <div className={`mx-auto max-w-7xl px-8 pt-4`}>
          <div className={`p-4 rounded-lg border ${
            welcomeMessage.type === 'success'
              ? 'bg-green-50 border-green-200 text-green-800'
              : 'bg-blue-50 border-blue-200 text-blue-800'
          }`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium">{welcomeMessage.text}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* User Role Information */}
      <div className="glass-effect border-b border-white/20 p-4 mt-16">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-slate-700">
              Current Role: {userRole?.charAt(0).toUpperCase() + userRole?.slice(1)}
              {currentOrganization && ` in ${currentOrganization.name}`}
            </span>
          </div>
          <div className="flex items-center gap-2">
            {/* Demo Role Switcher - Only show in development */}
            {process.env.NODE_ENV === 'development' && (
              <>
                <span className="text-xs text-slate-500 mr-2">Demo Mode:</span>
                {['owner', 'admin', 'member', 'viewer'].map((role) => (
                  <button
                    key={role}
                    onClick={() => handleRoleChange(role)}
                    className={`px-3 py-1 rounded text-xs font-medium transition-all duration-200 ${
                      userRole === role
                        ? 'bg-white text-slate-800 shadow-md'
                        : 'text-slate-600 hover:bg-white/50 hover:text-slate-800'
                    }`}
                  >
                    {role.charAt(0).toUpperCase() + role.slice(1)}
                  </button>
                ))}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Dashboard Header */}
      <DashboardHeader
        userRole={userRole}
        onFilterChange={setFilterValue}
        onSearchChange={setSearchValue}
        searchValue={searchValue}
      />

      {/* Main Dashboard Content */}
      <div className="max-w-7xl mx-auto p-8">
        {/* Enhanced KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10">
          {getKPIData().map((kpi, index) => (
            <div key={index} className="animate-fade-in" style={{ animationDelay: `${index * 0.1}s` }}>
              <KPICard
                title={kpi.title}
                value={kpi.value}
                change={kpi.change}
                changeType={kpi.changeType}
                icon={kpi.icon}
                color={kpi.color}
              />
            </div>
          ))}
        </div>

        {/* Main Content Grid with improved spacing */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-10">
          {/* Left Column - Projects and Quick Actions */}
          <div className="lg:col-span-2 space-y-8">
            {/* Projects Section with enhanced header */}
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-semibold text-slate-800 tracking-tight">
                    {userRole === 'Viewer' ? 'Available Projects' : 'My Projects'}
                  </h2>
                  <p className="text-slate-600 mt-1">Manage and track your active projects</p>
                </div>
                <Link 
                  to="/project-overview-analytics"
                  className="flex items-center gap-2 px-4 py-2 text-sm text-blue-600 hover:text-blue-700 font-medium bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200"
                >
                  View Analytics
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
              </div>
              
              <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
                {filteredProjects.map((project, index) => (
                  <div key={project.id} className="animate-fade-in" style={{ animationDelay: `${index * 0.1}s` }}>
                    <ProjectCard
                      project={project}
                      userRole={userRole}
                    />
                  </div>
                ))}
              </div>
              
              {filteredProjects.length === 0 && (
                <div className="text-center py-16 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/20">
                  <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <p className="text-slate-600 text-lg">No projects match your current filters</p>
                  <p className="text-slate-500 text-sm mt-1">Try adjusting your search or filter criteria</p>
                </div>
              )}
            </div>

            {/* Quick Actions */}
            <QuickActions userRole={userRole} />
          </div>

          {/* Right Column - Activity Feed and Notifications */}
          <div className="space-y-8">
            <ActivityFeed activities={mockActivities} userRole={userRole} />
            <NotificationPanel notifications={mockNotifications} userRole={userRole} />
          </div>
        </div>

        {/* Bottom Section - Tasks and Team with improved layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <TaskSummary tasks={mockTasks} userRole={userRole} />
          <TeamOverview teamMembers={mockTeamMembers} userRole={userRole} />
        </div>
      </div>
    </div>
  );
};

export default RoleBasedDashboard;