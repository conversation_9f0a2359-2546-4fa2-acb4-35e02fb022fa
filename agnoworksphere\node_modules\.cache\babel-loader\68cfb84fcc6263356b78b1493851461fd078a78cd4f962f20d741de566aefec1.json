{"ast": null, "code": "var _jsxFileName = \"D:\\\\PM\\\\agnoworksphere\\\\src\\\\Routes.jsx\";\nimport React from \"react\";\nimport { BrowserRouter, Routes as RouterRoutes, Route } from \"react-router-dom\";\nimport ScrollToTop from \"./components/ScrollToTop\";\nimport ErrorBoundary from \"./components/ErrorBoundary\";\n\n// Page imports\nimport Login from \"./pages/login\";\nimport Register from \"./pages/register\";\nimport KanbanBoard from \"./pages/kanban-board\";\nimport CardDetails from \"./pages/card-details\";\nimport TeamMembers from \"./pages/team-members\";\nimport OrganizationSettings from \"./pages/organization-settings\";\nimport OrganizationDashboard from \"./pages/organization-dashboard\";\nimport UserProfileSettings from \"./pages/user-profile-settings\";\nimport ProjectManagement from \"./pages/project-management\";\nimport RoleBasedDashboard from \"./pages/role-based-dashboard\";\nimport OrganizationSetup from \"./pages/organization-setup\";\nimport NotFound from \"./pages/NotFound\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Routes = () => {\n  return /*#__PURE__*/_jsxDEV(BrowserRouter, {\n    children: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n      children: /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(ScrollToTop, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(RouterRoutes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(OrganizationDashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/dashboard\",\n            element: /*#__PURE__*/_jsxDEV(OrganizationDashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/register\",\n            element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/kanban-board\",\n            element: /*#__PURE__*/_jsxDEV(KanbanBoard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 50\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/card-details\",\n            element: /*#__PURE__*/_jsxDEV(CardDetails, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 50\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/team-members\",\n            element: /*#__PURE__*/_jsxDEV(TeamMembers, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 50\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/organization-settings\",\n            element: /*#__PURE__*/_jsxDEV(OrganizationSettings, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 59\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user-profile-settings\",\n            element: /*#__PURE__*/_jsxDEV(UserProfileSettings, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 59\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/project-management\",\n            element: /*#__PURE__*/_jsxDEV(ProjectManagement, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 56\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/role-based-dashboard\",\n            element: /*#__PURE__*/_jsxDEV(RoleBasedDashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 58\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(NotFound, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_c = Routes;\nexport default Routes;\nvar _c;\n$RefreshReg$(_c, \"Routes\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "RouterRoutes", "Route", "ScrollToTop", "Error<PERSON>ou<PERSON><PERSON>", "<PERSON><PERSON>", "Register", "KanbanBoard", "CardDetails", "TeamMembers", "OrganizationSettings", "OrganizationDashboard", "UserProfileSettings", "ProjectManagement", "RoleBasedDashboard", "OrganizationSetup", "NotFound", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "_c", "$RefreshReg$"], "sources": ["D:/PM/agnoworksphere/src/Routes.jsx"], "sourcesContent": ["import React from \"react\";\nimport { BrowserRouter, Routes as RouterRoutes, Route } from \"react-router-dom\";\nimport ScrollToTop from \"./components/ScrollToTop\";\nimport ErrorBoundary from \"./components/ErrorBoundary\";\n\n// Page imports\nimport Login from \"./pages/login\";\nimport Register from \"./pages/register\";\nimport KanbanBoard from \"./pages/kanban-board\";\nimport CardDetails from \"./pages/card-details\";\nimport TeamMembers from \"./pages/team-members\";\nimport OrganizationSettings from \"./pages/organization-settings\";\nimport OrganizationDashboard from \"./pages/organization-dashboard\";\nimport UserProfileSettings from \"./pages/user-profile-settings\";\nimport ProjectManagement from \"./pages/project-management\";\nimport RoleBasedDashboard from \"./pages/role-based-dashboard\";\nimport OrganizationSetup from \"./pages/organization-setup\";\nimport NotFound from \"./pages/NotFound\";\n\nconst Routes = () => {\n  return (\n    <BrowserRouter>\n      <ErrorBoundary>\n        <>\n          <ScrollToTop />\n          <RouterRoutes>\n            <Route path=\"/\" element={<OrganizationDashboard />} />\n            <Route path=\"/dashboard\" element={<OrganizationDashboard />} />\n            <Route path=\"/login\" element={<Login />} />\n            <Route path=\"/register\" element={<Register />} />\n            <Route path=\"/kanban-board\" element={<KanbanBoard />} />\n            <Route path=\"/card-details\" element={<CardDetails />} />\n            <Route path=\"/team-members\" element={<TeamMembers />} />\n            <Route path=\"/organization-settings\" element={<OrganizationSettings />} />\n            <Route path=\"/user-profile-settings\" element={<UserProfileSettings />} />\n            <Route path=\"/project-management\" element={<ProjectManagement />} />\n            <Route path=\"/role-based-dashboard\" element={<RoleBasedDashboard />} />                 \n            <Route path=\"*\" element={<NotFound />} />\n          </RouterRoutes>\n        </>\n      </ErrorBoundary>\n    </BrowserRouter>\n  );\n};\n\nexport default Routes;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,EAAEC,MAAM,IAAIC,YAAY,EAAEC,KAAK,QAAQ,kBAAkB;AAC/E,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,aAAa,MAAM,4BAA4B;;AAEtD;AACA,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,oBAAoB,MAAM,+BAA+B;AAChE,OAAOC,qBAAqB,MAAM,gCAAgC;AAClE,OAAOC,mBAAmB,MAAM,+BAA+B;AAC/D,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,kBAAkB,MAAM,8BAA8B;AAC7D,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,QAAQ,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMpB,MAAM,GAAGA,CAAA,KAAM;EACnB,oBACEkB,OAAA,CAACnB,aAAa;IAAAsB,QAAA,eACZH,OAAA,CAACd,aAAa;MAAAiB,QAAA,eACZH,OAAA,CAAAE,SAAA;QAAAC,QAAA,gBACEH,OAAA,CAACf,WAAW;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACfP,OAAA,CAACjB,YAAY;UAAAoB,QAAA,gBACXH,OAAA,CAAChB,KAAK;YAACwB,IAAI,EAAC,GAAG;YAACC,OAAO,eAAET,OAAA,CAACP,qBAAqB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtDP,OAAA,CAAChB,KAAK;YAACwB,IAAI,EAAC,YAAY;YAACC,OAAO,eAAET,OAAA,CAACP,qBAAqB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/DP,OAAA,CAAChB,KAAK;YAACwB,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAET,OAAA,CAACb,KAAK;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3CP,OAAA,CAAChB,KAAK;YAACwB,IAAI,EAAC,WAAW;YAACC,OAAO,eAAET,OAAA,CAACZ,QAAQ;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDP,OAAA,CAAChB,KAAK;YAACwB,IAAI,EAAC,eAAe;YAACC,OAAO,eAAET,OAAA,CAACX,WAAW;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxDP,OAAA,CAAChB,KAAK;YAACwB,IAAI,EAAC,eAAe;YAACC,OAAO,eAAET,OAAA,CAACV,WAAW;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxDP,OAAA,CAAChB,KAAK;YAACwB,IAAI,EAAC,eAAe;YAACC,OAAO,eAAET,OAAA,CAACT,WAAW;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxDP,OAAA,CAAChB,KAAK;YAACwB,IAAI,EAAC,wBAAwB;YAACC,OAAO,eAAET,OAAA,CAACR,oBAAoB;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1EP,OAAA,CAAChB,KAAK;YAACwB,IAAI,EAAC,wBAAwB;YAACC,OAAO,eAAET,OAAA,CAACN,mBAAmB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzEP,OAAA,CAAChB,KAAK;YAACwB,IAAI,EAAC,qBAAqB;YAACC,OAAO,eAAET,OAAA,CAACL,iBAAiB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpEP,OAAA,CAAChB,KAAK;YAACwB,IAAI,EAAC,uBAAuB;YAACC,OAAO,eAAET,OAAA,CAACJ,kBAAkB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvEP,OAAA,CAAChB,KAAK;YAACwB,IAAI,EAAC,GAAG;YAACC,OAAO,eAAET,OAAA,CAACF,QAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA,eACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEpB,CAAC;AAACG,EAAA,GAxBI5B,MAAM;AA0BZ,eAAeA,MAAM;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}