// src/utils/roleBasedRouting.js

import { getOrganizations } from './organizationService';

/**
 * Determines the appropriate route for a user based on their role
 * @param {Object} user - The authenticated user object
 * @returns {Promise<string>} - The route path to redirect to
 */
export const determineUserRoute = async (user) => {
  if (!user) {
    return '/login';
  }

  try {
    // Get user's organizations
    const orgsResult = await getOrganizations();
    
    if (!orgsResult.data || orgsResult.data.length === 0) {
      // User has no organizations - redirect to setup
      return '/organization-setup';
    }

    // Get primary organization (first one)
    const primaryOrg = orgsResult.data[0];
    const userRole = primaryOrg.role;

    // Route based on role
    switch (userRole) {
      case 'owner':
      case 'admin':
        return '/role-based-dashboard';
      case 'member':
      case 'viewer':
        return '/kanban-board';
      default:
        return '/role-based-dashboard';
    }
  } catch (error) {
    console.error('Error determining user route:', error);
    // Fallback to role-based dashboard
    return '/role-based-dashboard';
  }
};

/**
 * Gets the user's role in their primary organization
 * @param {Object} user - The authenticated user object
 * @returns {Promise<Object>} - Object containing role and organization info
 */
export const getUserRoleInfo = async (user) => {
  if (!user) {
    return { role: null, organization: null, organizations: [] };
  }

  try {
    const orgsResult = await getOrganizations();
    
    if (!orgsResult.data || orgsResult.data.length === 0) {
      return { role: null, organization: null, organizations: [] };
    }

    const primaryOrg = orgsResult.data[0];
    
    return {
      role: primaryOrg.role,
      organization: primaryOrg,
      organizations: orgsResult.data
    };
  } catch (error) {
    console.error('Error getting user role info:', error);
    return { role: null, organization: null, organizations: [] };
  }
};

/**
 * Checks if a user has permission to access a specific route
 * @param {string} userRole - The user's role
 * @param {string} route - The route to check
 * @returns {boolean} - Whether the user can access the route
 */
export const canAccessRoute = (userRole, route) => {
  const rolePermissions = {
    owner: [
      '/role-based-dashboard',
      '/organization-settings',
      '/team-members',
      '/kanban-board',
      '/project-management',
      '/user-profile-settings'
    ],
    admin: [
      '/role-based-dashboard',
      '/organization-settings',
      '/team-members',
      '/kanban-board',
      '/project-management',
      '/user-profile-settings'
    ],
    member: [
      '/kanban-board',
      '/team-members',
      '/project-management',
      '/user-profile-settings',
      '/role-based-dashboard'
    ],
    viewer: [
      '/kanban-board',
      '/team-members',
      '/user-profile-settings',
      '/role-based-dashboard'
    ]
  };

  const allowedRoutes = rolePermissions[userRole] || [];
  return allowedRoutes.includes(route);
};

/**
 * Gets the default route for a specific role
 * @param {string} role - The user's role
 * @returns {string} - The default route for the role
 */
export const getDefaultRouteForRole = (role) => {
  switch (role) {
    case 'owner':
    case 'admin':
      return '/role-based-dashboard';
    case 'member':
    case 'viewer':
      return '/kanban-board';
    default:
      return '/role-based-dashboard';
  }
};

/**
 * Role hierarchy for permission checking
 */
export const ROLE_HIERARCHY = {
  owner: 4,
  admin: 3,
  member: 2,
  viewer: 1
};

/**
 * Checks if a user role has higher or equal permissions than required role
 * @param {string} userRole - The user's role
 * @param {string} requiredRole - The minimum required role
 * @returns {boolean} - Whether the user has sufficient permissions
 */
export const hasRolePermission = (userRole, requiredRole) => {
  const userLevel = ROLE_HIERARCHY[userRole] || 0;
  const requiredLevel = ROLE_HIERARCHY[requiredRole] || 0;
  return userLevel >= requiredLevel;
};
