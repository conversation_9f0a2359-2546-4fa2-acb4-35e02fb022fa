import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../contexts/AuthContext';
import { determineUserRoute } from '../../../utils/roleBasedRouting';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import Icon from '../../../components/AppIcon';

const LoginForm = () => {
  const navigate = useNavigate();
  const { login, loading: authLoading, error: authError, clearError } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Mock credentials for testing (can be removed in production)
  const mockCredentials = [
    { email: '<EMAIL>', password: 'Owner123!', role: 'Owner' },
    { email: '<EMAIL>', password: 'Admin123!', role: 'Admin' },
    { email: '<EMAIL>', password: 'Member123!', role: 'Member' },
    { email: '<EMAIL>', password: 'Viewer123!', role: 'Viewer' }
  ];

  const validateForm = () => {
    const newErrors = {};

    if (!formData.email) {
      newErrors.email = 'Email address is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }

    // Clear auth error when user starts typing
    if (authError) {
      clearError();
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      console.log('🔍 Debug: Starting login process');
      console.log('🔍 Debug: Form data:', { email: formData.email, password: '***' });
      console.log('🔍 Debug: Login function available:', typeof login);

      // Use real authentication service
      const result = await login(formData.email, formData.password);

      console.log('🔍 Debug: Login result:', result);

      if (result && result.success) {
        console.log('🔍 Debug: Login successful, determining route...');

        // Use utility function to determine appropriate route
        try {
          const route = await determineUserRoute(result.user);
          console.log('🔍 Debug: Redirecting to:', route);
          navigate(route);
        } catch (routeError) {
          console.error('🔍 Debug: Error determining route:', routeError);
          // Fallback to role-based dashboard
          navigate('/role-based-dashboard');
        }
      } else {
        console.log('🔍 Debug: Login failed:', result?.error);
        setErrors({
          general: result?.error || 'Invalid email or password. Please check your credentials and try again.'
        });
      }
    } catch (error) {
      console.error('🔍 Debug: Login error:', error);
      setErrors({
        general: `An error occurred during login: ${error.message || 'Please try again.'}`
      });
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-card border border-border rounded-lg shadow-enterprise p-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-2xl font-semibold text-text-primary mb-2">
            Welcome Back
          </h1>
          <p className="text-text-secondary">
            Sign in to your Agno WorkSphere account
          </p>
        </div>

        {/* Login Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* General Error */}
          {(errors.general || authError) && (
            <div className="bg-destructive/10 border border-destructive/20 rounded-md p-3 flex items-start space-x-2">
              <Icon name="AlertCircle" size={16} className="text-destructive mt-0.5 flex-shrink-0" />
              <p className="text-sm text-destructive">{errors.general || authError}</p>
            </div>
          )}

          {/* Email Field */}
          <div>
            <Input
              label="Email Address"
              type="email"
              name="email"
              placeholder="Enter your email address"
              value={formData.email}
              onChange={handleInputChange}
              error={errors.email}
              required
              disabled={isLoading}
            />
          </div>

          {/* Password Field */}
          <div>
            <div className="relative">
              <Input
                label="Password"
                type={showPassword ? "text" : "password"}
                name="password"
                placeholder="Enter your password"
                value={formData.password}
                onChange={handleInputChange}
                error={errors.password}
                required
                disabled={isLoading}
              />
              <button
                type="button"
                onClick={togglePasswordVisibility}
                className="absolute right-3 top-9 text-text-secondary hover:text-text-primary transition-micro"
                disabled={isLoading}
              >
                <Icon name={showPassword ? "EyeOff" : "Eye"} size={16} />
              </button>
            </div>
          </div>

          {/* Remember Me & Forgot Password */}
          <div className="flex items-center justify-between">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                className="w-4 h-4 text-primary border-border rounded focus:ring-primary focus:ring-2"
                disabled={isLoading}
              />
              <span className="text-sm text-text-secondary">Remember me</span>
            </label>
            <button
              type="button"
              className="text-sm text-primary hover:text-primary/80 transition-micro"
              disabled={isLoading}
            >
              Forgot password?
            </button>
          </div>

          {/* Sign In Button */}
          <Button
            type="submit"
            variant="default"
            fullWidth
            loading={isLoading || authLoading}
            disabled={isLoading || authLoading}
            className="h-12"
          >
            {(isLoading || authLoading) ? 'Signing In...' : 'Sign In'}
          </Button>
        </form>

        {/* Divider */}
        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-border"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-card text-text-secondary">Or continue with</span>
          </div>
        </div>

        {/* Social Login Options */}
        <div className="grid grid-cols-2 gap-3">
          <Button
            variant="outline"
            disabled={isLoading}
            className="h-10"
          >
            <Icon name="Mail" size={16} className="mr-2" />
            Google
          </Button>
          <Button
            variant="outline"
            disabled={isLoading}
            className="h-10"
          >
            <Icon name="Github" size={16} className="mr-2" />
            GitHub
          </Button>
        </div>

        {/* Sign Up Link */}
        <div className="text-center mt-6">
          <p className="text-sm text-text-secondary">
            Don't have an account?{' '}
            <button
              type="button"
              onClick={() => navigate('/register')}
              className="text-primary hover:text-primary/80 font-medium transition-micro"
              disabled={isLoading}
            >
              Create account
            </button>
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;