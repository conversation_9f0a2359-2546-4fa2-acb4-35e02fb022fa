{"ast": null, "code": "var _jsxFileName = \"D:\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\register\\\\components\\\\RegistrationForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../contexts/AuthContext';\nimport { createOrganization } from '../../../utils/organizationService';\nimport Button from '../../../components/ui/Button';\nimport Input from '../../../components/ui/Input';\nimport { Checkbox } from '../../../components/ui/Checkbox';\nimport Icon from '../../../components/AppIcon';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RegistrationForm = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    register\n  } = useAuth();\n  const [currentStep, setCurrentStep] = useState(1);\n  const [isLoading, setIsLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    fullName: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    organizationName: '',\n    organizationDomain: '',\n    role: 'owner',\n    agreeToTerms: false,\n    agreeToPrivacy: false\n  });\n  const [errors, setErrors] = useState({});\n  const [passwordStrength, setPasswordStrength] = useState(0);\n  const validateEmail = email => {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  };\n  const validatePassword = password => {\n    let strength = 0;\n    if (password.length >= 8) strength++;\n    if (/[A-Z]/.test(password)) strength++;\n    if (/[a-z]/.test(password)) strength++;\n    if (/[0-9]/.test(password)) strength++;\n    if (/[^A-Za-z0-9]/.test(password)) strength++;\n    return strength;\n  };\n  const validateDomain = domain => {\n    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\\.[a-zA-Z]{2,})+$/;\n    return domainRegex.test(domain);\n  };\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n\n    // Clear specific field error\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }));\n    }\n\n    // Real-time password strength validation\n    if (field === 'password') {\n      setPasswordStrength(validatePassword(value));\n    }\n  };\n  const validateStep1 = () => {\n    const newErrors = {};\n    if (!formData.fullName.trim()) {\n      newErrors.fullName = 'Full name is required';\n    }\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email address is required';\n    } else if (!validateEmail(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 8) {\n      newErrors.password = 'Password must be at least 8 characters long';\n    }\n    if (!formData.confirmPassword) {\n      newErrors.confirmPassword = 'Please confirm your password';\n    } else if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const validateStep2 = () => {\n    const newErrors = {};\n    if (!formData.organizationName.trim()) {\n      newErrors.organizationName = 'Organization name is required';\n    }\n    if (!formData.organizationDomain.trim()) {\n      newErrors.organizationDomain = 'Organization domain is required';\n    } else if (!validateDomain(formData.organizationDomain)) {\n      newErrors.organizationDomain = 'Please enter a valid domain (e.g., company.com)';\n    }\n    if (!formData.agreeToTerms) {\n      newErrors.agreeToTerms = 'You must agree to the Terms of Service';\n    }\n    if (!formData.agreeToPrivacy) {\n      newErrors.agreeToPrivacy = 'You must agree to the Privacy Policy';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleNextStep = () => {\n    if (validateStep1()) {\n      setCurrentStep(2);\n    }\n  };\n  const handlePreviousStep = () => {\n    setCurrentStep(1);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    console.log('Form submission started with data:', formData);\n    if (!validateStep2()) {\n      console.log('Step 2 validation failed');\n      return;\n    }\n    console.log('Step 2 validation passed, starting registration...');\n    setIsLoading(true);\n    try {\n      // Extract first and last name from full name\n      const nameParts = formData.fullName.trim().split(' ');\n      const firstName = nameParts[0] || '';\n      const lastName = nameParts.slice(1).join(' ') || '';\n\n      // Register user with real API\n      console.log('Calling register with:', {\n        email: formData.email,\n        password: '***',\n        userData: {\n          firstName,\n          lastName\n        }\n      });\n      const registrationResult = await register(formData.email, formData.password, {\n        firstName,\n        lastName\n      });\n      console.log('Registration result:', registrationResult);\n      if (!registrationResult.success) {\n        console.error('Registration failed:', registrationResult.error);\n        throw new Error(registrationResult.error || 'Registration failed');\n      }\n      console.log('Registration successful, user:', registrationResult.user);\n\n      // Wait for token to be properly set and verify it's available\n      let retries = 0;\n      const maxRetries = 10;\n      while (retries < maxRetries) {\n        await new Promise(resolve => setTimeout(resolve, 200));\n        const token = localStorage.getItem('agno_auth_token');\n        if (token) {\n          console.log('Token found after', retries + 1, 'attempts');\n          break;\n        }\n        retries++;\n      }\n\n      // Auto-create organization for new user\n      try {\n        console.log('Creating organization with data:', {\n          name: formData.organizationName,\n          description: `${formData.organizationName} workspace`,\n          domain: formData.organizationDomain\n        });\n        const organizationResult = await createOrganization({\n          name: formData.organizationName,\n          description: `${formData.organizationName} workspace`,\n          domain: formData.organizationDomain\n        });\n        console.log('Organization creation result:', organizationResult);\n        if (!organizationResult.data) {\n          console.warn('Organization creation failed:', organizationResult.error);\n          // Continue anyway - user can create organization later\n        }\n      } catch (orgError) {\n        console.warn('Organization creation failed:', orgError.message);\n        // Continue anyway - user can create organization later\n      }\n\n      // Navigate to role-based dashboard\n      navigate('/role-based-dashboard', {\n        state: {\n          message: 'Account created successfully! Welcome to your new workspace.',\n          type: 'success',\n          isNewUser: true\n        }\n      });\n    } catch (error) {\n      console.error('Registration error:', error);\n      setErrors({\n        submit: error.message || 'Registration failed. Please try again.'\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const getPasswordStrengthText = () => {\n    switch (passwordStrength) {\n      case 0:\n      case 1:\n        return 'Weak';\n      case 2:\n      case 3:\n        return 'Medium';\n      case 4:\n      case 5:\n        return 'Strong';\n      default:\n        return '';\n    }\n  };\n  const getPasswordStrengthColor = () => {\n    switch (passwordStrength) {\n      case 0:\n      case 1:\n        return 'bg-destructive';\n      case 2:\n      case 3:\n        return 'bg-warning';\n      case 4:\n      case 5:\n        return 'bg-success';\n      default:\n        return 'bg-muted';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full max-w-md mx-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card rounded-lg shadow-enterprise border border-border p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-semibold text-text-primary mb-2\",\n          children: \"Create Your Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-text-secondary\",\n          children: currentStep === 1 ? 'Enter your personal information to get started' : 'Set up your organization workspace'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center justify-center w-8 h-8 rounded-full border-2 ${currentStep >= 1 ? 'bg-primary border-primary text-primary-foreground' : 'border-border text-text-secondary'}`,\n            children: currentStep > 1 ? /*#__PURE__*/_jsxDEV(Icon, {\n              name: \"Check\",\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 34\n            }, this) : '1'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `w-12 h-0.5 ${currentStep > 1 ? 'bg-primary' : 'bg-border'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center justify-center w-8 h-8 rounded-full border-2 ${currentStep >= 2 ? 'bg-primary border-primary text-primary-foreground' : 'border-border text-text-secondary'}`,\n            children: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-6\",\n        children: [currentStep === 1 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            label: \"Full Name\",\n            type: \"text\",\n            placeholder: \"Enter your full name\",\n            value: formData.fullName,\n            onChange: e => handleInputChange('fullName', e.target.value),\n            error: errors.fullName,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Email Address\",\n            type: \"email\",\n            placeholder: \"Enter your email address\",\n            value: formData.email,\n            onChange: e => handleInputChange('email', e.target.value),\n            error: errors.email,\n            description: \"We'll use this email for account verification and notifications\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              label: \"Password\",\n              type: \"password\",\n              placeholder: \"Create a strong password\",\n              value: formData.password,\n              onChange: e => handleInputChange('password', e.target.value),\n              error: errors.password,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 17\n            }, this), formData.password && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-text-secondary\",\n                  children: \"Password strength:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `font-medium ${passwordStrength <= 1 ? 'text-destructive' : passwordStrength <= 3 ? 'text-warning' : 'text-success'}`,\n                  children: getPasswordStrengthText()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-muted rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `h-2 rounded-full transition-all duration-300 ${getPasswordStrengthColor()}`,\n                  style: {\n                    width: `${passwordStrength / 5 * 100}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Confirm Password\",\n            type: \"password\",\n            placeholder: \"Confirm your password\",\n            value: formData.confirmPassword,\n            onChange: e => handleInputChange('confirmPassword', e.target.value),\n            error: errors.confirmPassword,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"button\",\n            onClick: handleNextStep,\n            className: \"w-full\",\n            iconName: \"ArrowRight\",\n            iconPosition: \"right\",\n            children: \"Continue\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), currentStep === 2 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            label: \"Organization Name\",\n            type: \"text\",\n            placeholder: \"Enter your organization name\",\n            value: formData.organizationName,\n            onChange: e => handleInputChange('organizationName', e.target.value),\n            error: errors.organizationName,\n            description: \"This will be the name of your workspace\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Organization Domain\",\n            type: \"text\",\n            placeholder: \"company.com\",\n            value: formData.organizationDomain,\n            onChange: e => handleInputChange('organizationDomain', e.target.value),\n            error: errors.organizationDomain,\n            description: \"Used for email invitations and domain restrictions\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n              label: \"I agree to the Terms of Service\",\n              checked: formData.agreeToTerms,\n              onChange: e => handleInputChange('agreeToTerms', e.target.checked),\n              error: errors.agreeToTerms,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Checkbox, {\n              label: \"I agree to the Privacy Policy\",\n              checked: formData.agreeToPrivacy,\n              onChange: e => handleInputChange('agreeToPrivacy', e.target.checked),\n              error: errors.agreeToPrivacy,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 15\n          }, this), errors.submit && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 bg-destructive/10 border border-destructive/20 rounded-md\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-destructive\",\n              children: errors.submit\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"button\",\n              variant: \"outline\",\n              onClick: handlePreviousStep,\n              className: \"flex-1\",\n              iconName: \"ArrowLeft\",\n              iconPosition: \"left\",\n              children: \"Back\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"submit\",\n              loading: isLoading,\n              className: \"flex-1\",\n              iconName: \"UserPlus\",\n              iconPosition: \"left\",\n              children: \"Create Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 pt-6 border-t border-border text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-text-secondary\",\n          children: [\"Already have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"font-medium text-primary hover:text-primary/80 transition-colors\",\n            children: \"Sign in here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 446,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-text-secondary\",\n        children: [\"By creating an account, you'll be able to invite team members and manage projects.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this), \"Need to join an existing organization? Contact your administrator for an invitation.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 253,\n    columnNumber: 5\n  }, this);\n};\n_s(RegistrationForm, \"4sMp8KI69eG/wrkEnlfdwb8+gNo=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = RegistrationForm;\nexport default RegistrationForm;\nvar _c;\n$RefreshReg$(_c, \"RegistrationForm\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "useAuth", "createOrganization", "<PERSON><PERSON>", "Input", "Checkbox", "Icon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RegistrationForm", "_s", "navigate", "register", "currentStep", "setCurrentStep", "isLoading", "setIsLoading", "formData", "setFormData", "fullName", "email", "password", "confirmPassword", "organizationName", "organizationDomain", "role", "agreeToTerms", "agreeToPrivacy", "errors", "setErrors", "passwordStrength", "setPasswordStrength", "validateEmail", "emailRegex", "test", "validatePassword", "strength", "length", "validateDomain", "domain", "domainRegex", "handleInputChange", "field", "value", "prev", "validateStep1", "newErrors", "trim", "Object", "keys", "validateStep2", "handleNextStep", "handlePreviousStep", "handleSubmit", "e", "preventDefault", "console", "log", "nameParts", "split", "firstName", "lastName", "slice", "join", "userData", "registrationResult", "success", "error", "Error", "user", "retries", "maxRetries", "Promise", "resolve", "setTimeout", "token", "localStorage", "getItem", "name", "description", "organizationResult", "data", "warn", "orgError", "message", "state", "type", "isNewUser", "submit", "getPasswordStrengthText", "getPasswordStrengthColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "onSubmit", "label", "placeholder", "onChange", "target", "required", "style", "width", "onClick", "iconName", "iconPosition", "checked", "variant", "loading", "to", "_c", "$RefreshReg$"], "sources": ["D:/PM/agnoworksphere/src/pages/register/components/RegistrationForm.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../contexts/AuthContext';\nimport { createOrganization } from '../../../utils/organizationService';\nimport Button from '../../../components/ui/Button';\nimport Input from '../../../components/ui/Input';\nimport { Checkbox } from '../../../components/ui/Checkbox';\nimport Icon from '../../../components/AppIcon';\n\nconst RegistrationForm = () => {\n  const navigate = useNavigate();\n  const { register } = useAuth();\n  const [currentStep, setCurrentStep] = useState(1);\n  const [isLoading, setIsLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    fullName: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    organizationName: '',\n    organizationDomain: '',\n    role: 'owner',\n    agreeToTerms: false,\n    agreeToPrivacy: false\n  });\n  const [errors, setErrors] = useState({});\n  const [passwordStrength, setPasswordStrength] = useState(0);\n\n  const validateEmail = (email) => {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  };\n\n  const validatePassword = (password) => {\n    let strength = 0;\n    if (password.length >= 8) strength++;\n    if (/[A-Z]/.test(password)) strength++;\n    if (/[a-z]/.test(password)) strength++;\n    if (/[0-9]/.test(password)) strength++;\n    if (/[^A-Za-z0-9]/.test(password)) strength++;\n    return strength;\n  };\n\n  const validateDomain = (domain) => {\n    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\\.[a-zA-Z]{2,})+$/;\n    return domainRegex.test(domain);\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    \n    // Clear specific field error\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: '' }));\n    }\n\n    // Real-time password strength validation\n    if (field === 'password') {\n      setPasswordStrength(validatePassword(value));\n    }\n  };\n\n  const validateStep1 = () => {\n    const newErrors = {};\n\n    if (!formData.fullName.trim()) {\n      newErrors.fullName = 'Full name is required';\n    }\n\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email address is required';\n    } else if (!validateEmail(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 8) {\n      newErrors.password = 'Password must be at least 8 characters long';\n    }\n\n    if (!formData.confirmPassword) {\n      newErrors.confirmPassword = 'Please confirm your password';\n    } else if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const validateStep2 = () => {\n    const newErrors = {};\n\n    if (!formData.organizationName.trim()) {\n      newErrors.organizationName = 'Organization name is required';\n    }\n\n    if (!formData.organizationDomain.trim()) {\n      newErrors.organizationDomain = 'Organization domain is required';\n    } else if (!validateDomain(formData.organizationDomain)) {\n      newErrors.organizationDomain = 'Please enter a valid domain (e.g., company.com)';\n    }\n\n    if (!formData.agreeToTerms) {\n      newErrors.agreeToTerms = 'You must agree to the Terms of Service';\n    }\n\n    if (!formData.agreeToPrivacy) {\n      newErrors.agreeToPrivacy = 'You must agree to the Privacy Policy';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleNextStep = () => {\n    if (validateStep1()) {\n      setCurrentStep(2);\n    }\n  };\n\n  const handlePreviousStep = () => {\n    setCurrentStep(1);\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    console.log('Form submission started with data:', formData);\n\n    if (!validateStep2()) {\n      console.log('Step 2 validation failed');\n      return;\n    }\n\n    console.log('Step 2 validation passed, starting registration...');\n    setIsLoading(true);\n\n    try {\n      // Extract first and last name from full name\n      const nameParts = formData.fullName.trim().split(' ');\n      const firstName = nameParts[0] || '';\n      const lastName = nameParts.slice(1).join(' ') || '';\n\n      // Register user with real API\n      console.log('Calling register with:', {\n        email: formData.email,\n        password: '***',\n        userData: { firstName, lastName }\n      });\n\n      const registrationResult = await register(\n        formData.email,\n        formData.password,\n        {\n          firstName,\n          lastName\n        }\n      );\n\n      console.log('Registration result:', registrationResult);\n\n      if (!registrationResult.success) {\n        console.error('Registration failed:', registrationResult.error);\n        throw new Error(registrationResult.error || 'Registration failed');\n      }\n\n      console.log('Registration successful, user:', registrationResult.user);\n\n      // Wait for token to be properly set and verify it's available\n      let retries = 0;\n      const maxRetries = 10;\n      while (retries < maxRetries) {\n        await new Promise(resolve => setTimeout(resolve, 200));\n        const token = localStorage.getItem('agno_auth_token');\n        if (token) {\n          console.log('Token found after', retries + 1, 'attempts');\n          break;\n        }\n        retries++;\n      }\n\n      // Auto-create organization for new user\n      try {\n        console.log('Creating organization with data:', {\n          name: formData.organizationName,\n          description: `${formData.organizationName} workspace`,\n          domain: formData.organizationDomain\n        });\n\n        const organizationResult = await createOrganization({\n          name: formData.organizationName,\n          description: `${formData.organizationName} workspace`,\n          domain: formData.organizationDomain\n        });\n\n        console.log('Organization creation result:', organizationResult);\n\n        if (!organizationResult.data) {\n          console.warn('Organization creation failed:', organizationResult.error);\n          // Continue anyway - user can create organization later\n        }\n      } catch (orgError) {\n        console.warn('Organization creation failed:', orgError.message);\n        // Continue anyway - user can create organization later\n      }\n\n      // Navigate to role-based dashboard\n      navigate('/role-based-dashboard', {\n        state: {\n          message: 'Account created successfully! Welcome to your new workspace.',\n          type: 'success',\n          isNewUser: true\n        }\n      });\n\n    } catch (error) {\n      console.error('Registration error:', error);\n      setErrors({\n        submit: error.message || 'Registration failed. Please try again.'\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const getPasswordStrengthText = () => {\n    switch (passwordStrength) {\n      case 0:\n      case 1: return 'Weak';\n      case 2:\n      case 3: return 'Medium';\n      case 4:\n      case 5: return 'Strong';\n      default: return '';\n    }\n  };\n\n  const getPasswordStrengthColor = () => {\n    switch (passwordStrength) {\n      case 0:\n      case 1: return 'bg-destructive';\n      case 2:\n      case 3: return 'bg-warning';\n      case 4:\n      case 5: return 'bg-success';\n      default: return 'bg-muted';\n    }\n  };\n\n  return (\n    <div className=\"w-full max-w-md mx-auto\">\n      <div className=\"bg-card rounded-lg shadow-enterprise border border-border p-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-2xl font-semibold text-text-primary mb-2\">\n            Create Your Account\n          </h1>\n          <p className=\"text-text-secondary\">\n            {currentStep === 1 \n              ? 'Enter your personal information to get started'\n              : 'Set up your organization workspace'\n            }\n          </p>\n        </div>\n\n        {/* Progress Indicator */}\n        <div className=\"flex items-center justify-center mb-8\">\n          <div className=\"flex items-center space-x-4\">\n            <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${\n              currentStep >= 1 \n                ? 'bg-primary border-primary text-primary-foreground' \n                : 'border-border text-text-secondary'\n            }`}>\n              {currentStep > 1 ? <Icon name=\"Check\" size={16} /> : '1'}\n            </div>\n            <div className={`w-12 h-0.5 ${currentStep > 1 ? 'bg-primary' : 'bg-border'}`} />\n            <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${\n              currentStep >= 2 \n                ? 'bg-primary border-primary text-primary-foreground' \n                : 'border-border text-text-secondary'\n            }`}>\n              2\n            </div>\n          </div>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {currentStep === 1 && (\n            <>\n              {/* Step 1: Personal Information */}\n              <Input\n                label=\"Full Name\"\n                type=\"text\"\n                placeholder=\"Enter your full name\"\n                value={formData.fullName}\n                onChange={(e) => handleInputChange('fullName', e.target.value)}\n                error={errors.fullName}\n                required\n              />\n\n              <Input\n                label=\"Email Address\"\n                type=\"email\"\n                placeholder=\"Enter your email address\"\n                value={formData.email}\n                onChange={(e) => handleInputChange('email', e.target.value)}\n                error={errors.email}\n                description=\"We'll use this email for account verification and notifications\"\n                required\n              />\n\n              <div className=\"space-y-2\">\n                <Input\n                  label=\"Password\"\n                  type=\"password\"\n                  placeholder=\"Create a strong password\"\n                  value={formData.password}\n                  onChange={(e) => handleInputChange('password', e.target.value)}\n                  error={errors.password}\n                  required\n                />\n                \n                {formData.password && (\n                  <div className=\"space-y-2\">\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-text-secondary\">Password strength:</span>\n                      <span className={`font-medium ${\n                        passwordStrength <= 1 ? 'text-destructive' :\n                        passwordStrength <= 3 ? 'text-warning' : 'text-success'\n                      }`}>\n                        {getPasswordStrengthText()}\n                      </span>\n                    </div>\n                    <div className=\"w-full bg-muted rounded-full h-2\">\n                      <div \n                        className={`h-2 rounded-full transition-all duration-300 ${getPasswordStrengthColor()}`}\n                        style={{ width: `${(passwordStrength / 5) * 100}%` }}\n                      />\n                    </div>\n                  </div>\n                )}\n              </div>\n\n              <Input\n                label=\"Confirm Password\"\n                type=\"password\"\n                placeholder=\"Confirm your password\"\n                value={formData.confirmPassword}\n                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}\n                error={errors.confirmPassword}\n                required\n              />\n\n              <Button\n                type=\"button\"\n                onClick={handleNextStep}\n                className=\"w-full\"\n                iconName=\"ArrowRight\"\n                iconPosition=\"right\"\n              >\n                Continue\n              </Button>\n            </>\n          )}\n\n          {currentStep === 2 && (\n            <>\n              {/* Step 2: Organization Setup */}\n              <Input\n                label=\"Organization Name\"\n                type=\"text\"\n                placeholder=\"Enter your organization name\"\n                value={formData.organizationName}\n                onChange={(e) => handleInputChange('organizationName', e.target.value)}\n                error={errors.organizationName}\n                description=\"This will be the name of your workspace\"\n                required\n              />\n\n              <Input\n                label=\"Organization Domain\"\n                type=\"text\"\n                placeholder=\"company.com\"\n                value={formData.organizationDomain}\n                onChange={(e) => handleInputChange('organizationDomain', e.target.value)}\n                error={errors.organizationDomain}\n                description=\"Used for email invitations and domain restrictions\"\n                required\n              />\n\n              {/* Terms and Privacy */}\n              <div className=\"space-y-4\">\n                <Checkbox\n                  label=\"I agree to the Terms of Service\"\n                  checked={formData.agreeToTerms}\n                  onChange={(e) => handleInputChange('agreeToTerms', e.target.checked)}\n                  error={errors.agreeToTerms}\n                  required\n                />\n\n                <Checkbox\n                  label=\"I agree to the Privacy Policy\"\n                  checked={formData.agreeToPrivacy}\n                  onChange={(e) => handleInputChange('agreeToPrivacy', e.target.checked)}\n                  error={errors.agreeToPrivacy}\n                  required\n                />\n              </div>\n\n              {errors.submit && (\n                <div className=\"p-3 bg-destructive/10 border border-destructive/20 rounded-md\">\n                  <p className=\"text-sm text-destructive\">{errors.submit}</p>\n                </div>\n              )}\n\n              {/* Action Buttons */}\n              <div className=\"flex space-x-3\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={handlePreviousStep}\n                  className=\"flex-1\"\n                  iconName=\"ArrowLeft\"\n                  iconPosition=\"left\"\n                >\n                  Back\n                </Button>\n                \n                <Button\n                  type=\"submit\"\n                  loading={isLoading}\n                  className=\"flex-1\"\n                  iconName=\"UserPlus\"\n                  iconPosition=\"left\"\n                >\n                  Create Account\n                </Button>\n              </div>\n            </>\n          )}\n        </form>\n\n        {/* Footer */}\n        <div className=\"mt-8 pt-6 border-t border-border text-center\">\n          <p className=\"text-sm text-text-secondary\">\n            Already have an account?{' '}\n            <Link \n              to=\"/login\" \n              className=\"font-medium text-primary hover:text-primary/80 transition-colors\"\n            >\n              Sign in here\n            </Link>\n          </p>\n        </div>\n      </div>\n\n      {/* Additional Information */}\n      <div className=\"mt-6 text-center\">\n        <p className=\"text-xs text-text-secondary\">\n          By creating an account, you'll be able to invite team members and manage projects.\n          <br />\n          Need to join an existing organization? Contact your administrator for an invitation.\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default RegistrationForm;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,+BAA+B;AACvD,SAASC,kBAAkB,QAAQ,oCAAoC;AACvE,OAAOC,MAAM,MAAM,+BAA+B;AAClD,OAAOC,KAAK,MAAM,8BAA8B;AAChD,SAASC,QAAQ,QAAQ,iCAAiC;AAC1D,OAAOC,IAAI,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEc;EAAS,CAAC,GAAGb,OAAO,CAAC,CAAC;EAC9B,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC;IACvCuB,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,gBAAgB,EAAE,EAAE;IACpBC,kBAAkB,EAAE,EAAE;IACtBC,IAAI,EAAE,OAAO;IACbC,YAAY,EAAE,KAAK;IACnBC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACkC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EAE3D,MAAMoC,aAAa,GAAIZ,KAAK,IAAK;IAC/B,MAAMa,UAAU,GAAG,4BAA4B;IAC/C,OAAOA,UAAU,CAACC,IAAI,CAACd,KAAK,CAAC;EAC/B,CAAC;EAED,MAAMe,gBAAgB,GAAId,QAAQ,IAAK;IACrC,IAAIe,QAAQ,GAAG,CAAC;IAChB,IAAIf,QAAQ,CAACgB,MAAM,IAAI,CAAC,EAAED,QAAQ,EAAE;IACpC,IAAI,OAAO,CAACF,IAAI,CAACb,QAAQ,CAAC,EAAEe,QAAQ,EAAE;IACtC,IAAI,OAAO,CAACF,IAAI,CAACb,QAAQ,CAAC,EAAEe,QAAQ,EAAE;IACtC,IAAI,OAAO,CAACF,IAAI,CAACb,QAAQ,CAAC,EAAEe,QAAQ,EAAE;IACtC,IAAI,cAAc,CAACF,IAAI,CAACb,QAAQ,CAAC,EAAEe,QAAQ,EAAE;IAC7C,OAAOA,QAAQ;EACjB,CAAC;EAED,MAAME,cAAc,GAAIC,MAAM,IAAK;IACjC,MAAMC,WAAW,GAAG,+DAA+D;IACnF,OAAOA,WAAW,CAACN,IAAI,CAACK,MAAM,CAAC;EACjC,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1CzB,WAAW,CAAC0B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;;IAElD;IACA,IAAIf,MAAM,CAACc,KAAK,CAAC,EAAE;MACjBb,SAAS,CAACe,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACF,KAAK,GAAG;MAAG,CAAC,CAAC,CAAC;IAC/C;;IAEA;IACA,IAAIA,KAAK,KAAK,UAAU,EAAE;MACxBX,mBAAmB,CAACI,gBAAgB,CAACQ,KAAK,CAAC,CAAC;IAC9C;EACF,CAAC;EAED,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAAC7B,QAAQ,CAACE,QAAQ,CAAC4B,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAAC3B,QAAQ,GAAG,uBAAuB;IAC9C;IAEA,IAAI,CAACF,QAAQ,CAACG,KAAK,CAAC2B,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAAC1B,KAAK,GAAG,2BAA2B;IAC/C,CAAC,MAAM,IAAI,CAACY,aAAa,CAACf,QAAQ,CAACG,KAAK,CAAC,EAAE;MACzC0B,SAAS,CAAC1B,KAAK,GAAG,oCAAoC;IACxD;IAEA,IAAI,CAACH,QAAQ,CAACI,QAAQ,EAAE;MACtByB,SAAS,CAACzB,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIJ,QAAQ,CAACI,QAAQ,CAACgB,MAAM,GAAG,CAAC,EAAE;MACvCS,SAAS,CAACzB,QAAQ,GAAG,6CAA6C;IACpE;IAEA,IAAI,CAACJ,QAAQ,CAACK,eAAe,EAAE;MAC7BwB,SAAS,CAACxB,eAAe,GAAG,8BAA8B;IAC5D,CAAC,MAAM,IAAIL,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,EAAE;MACzDwB,SAAS,CAACxB,eAAe,GAAG,wBAAwB;IACtD;IAEAO,SAAS,CAACiB,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACT,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMa,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMJ,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAAC7B,QAAQ,CAACM,gBAAgB,CAACwB,IAAI,CAAC,CAAC,EAAE;MACrCD,SAAS,CAACvB,gBAAgB,GAAG,+BAA+B;IAC9D;IAEA,IAAI,CAACN,QAAQ,CAACO,kBAAkB,CAACuB,IAAI,CAAC,CAAC,EAAE;MACvCD,SAAS,CAACtB,kBAAkB,GAAG,iCAAiC;IAClE,CAAC,MAAM,IAAI,CAACc,cAAc,CAACrB,QAAQ,CAACO,kBAAkB,CAAC,EAAE;MACvDsB,SAAS,CAACtB,kBAAkB,GAAG,iDAAiD;IAClF;IAEA,IAAI,CAACP,QAAQ,CAACS,YAAY,EAAE;MAC1BoB,SAAS,CAACpB,YAAY,GAAG,wCAAwC;IACnE;IAEA,IAAI,CAACT,QAAQ,CAACU,cAAc,EAAE;MAC5BmB,SAAS,CAACnB,cAAc,GAAG,sCAAsC;IACnE;IAEAE,SAAS,CAACiB,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACT,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMc,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIN,aAAa,CAAC,CAAC,EAAE;MACnB/B,cAAc,CAAC,CAAC,CAAC;IACnB;EACF,CAAC;EAED,MAAMsC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BtC,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;EAED,MAAMuC,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElBC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAExC,QAAQ,CAAC;IAE3D,IAAI,CAACiC,aAAa,CAAC,CAAC,EAAE;MACpBM,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC;IACF;IAEAD,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;IACjEzC,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF;MACA,MAAM0C,SAAS,GAAGzC,QAAQ,CAACE,QAAQ,CAAC4B,IAAI,CAAC,CAAC,CAACY,KAAK,CAAC,GAAG,CAAC;MACrD,MAAMC,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE;MACpC,MAAMG,QAAQ,GAAGH,SAAS,CAACI,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;;MAEnD;MACAP,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;QACpCrC,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBC,QAAQ,EAAE,KAAK;QACf2C,QAAQ,EAAE;UAAEJ,SAAS;UAAEC;QAAS;MAClC,CAAC,CAAC;MAEF,MAAMI,kBAAkB,GAAG,MAAMrD,QAAQ,CACvCK,QAAQ,CAACG,KAAK,EACdH,QAAQ,CAACI,QAAQ,EACjB;QACEuC,SAAS;QACTC;MACF,CACF,CAAC;MAEDL,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEQ,kBAAkB,CAAC;MAEvD,IAAI,CAACA,kBAAkB,CAACC,OAAO,EAAE;QAC/BV,OAAO,CAACW,KAAK,CAAC,sBAAsB,EAAEF,kBAAkB,CAACE,KAAK,CAAC;QAC/D,MAAM,IAAIC,KAAK,CAACH,kBAAkB,CAACE,KAAK,IAAI,qBAAqB,CAAC;MACpE;MAEAX,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEQ,kBAAkB,CAACI,IAAI,CAAC;;MAEtE;MACA,IAAIC,OAAO,GAAG,CAAC;MACf,MAAMC,UAAU,GAAG,EAAE;MACrB,OAAOD,OAAO,GAAGC,UAAU,EAAE;QAC3B,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;QACtD,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC;QACrD,IAAIF,KAAK,EAAE;UACTnB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEa,OAAO,GAAG,CAAC,EAAE,UAAU,CAAC;UACzD;QACF;QACAA,OAAO,EAAE;MACX;;MAEA;MACA,IAAI;QACFd,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;UAC9CqB,IAAI,EAAE7D,QAAQ,CAACM,gBAAgB;UAC/BwD,WAAW,EAAE,GAAG9D,QAAQ,CAACM,gBAAgB,YAAY;UACrDgB,MAAM,EAAEtB,QAAQ,CAACO;QACnB,CAAC,CAAC;QAEF,MAAMwD,kBAAkB,GAAG,MAAMhF,kBAAkB,CAAC;UAClD8E,IAAI,EAAE7D,QAAQ,CAACM,gBAAgB;UAC/BwD,WAAW,EAAE,GAAG9D,QAAQ,CAACM,gBAAgB,YAAY;UACrDgB,MAAM,EAAEtB,QAAQ,CAACO;QACnB,CAAC,CAAC;QAEFgC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEuB,kBAAkB,CAAC;QAEhE,IAAI,CAACA,kBAAkB,CAACC,IAAI,EAAE;UAC5BzB,OAAO,CAAC0B,IAAI,CAAC,+BAA+B,EAAEF,kBAAkB,CAACb,KAAK,CAAC;UACvE;QACF;MACF,CAAC,CAAC,OAAOgB,QAAQ,EAAE;QACjB3B,OAAO,CAAC0B,IAAI,CAAC,+BAA+B,EAAEC,QAAQ,CAACC,OAAO,CAAC;QAC/D;MACF;;MAEA;MACAzE,QAAQ,CAAC,uBAAuB,EAAE;QAChC0E,KAAK,EAAE;UACLD,OAAO,EAAE,8DAA8D;UACvEE,IAAI,EAAE,SAAS;UACfC,SAAS,EAAE;QACb;MACF,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CtC,SAAS,CAAC;QACR2D,MAAM,EAAErB,KAAK,CAACiB,OAAO,IAAI;MAC3B,CAAC,CAAC;IACJ,CAAC,SAAS;MACRpE,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMyE,uBAAuB,GAAGA,CAAA,KAAM;IACpC,QAAQ3D,gBAAgB;MACtB,KAAK,CAAC;MACN,KAAK,CAAC;QAAE,OAAO,MAAM;MACrB,KAAK,CAAC;MACN,KAAK,CAAC;QAAE,OAAO,QAAQ;MACvB,KAAK,CAAC;MACN,KAAK,CAAC;QAAE,OAAO,QAAQ;MACvB;QAAS,OAAO,EAAE;IACpB;EACF,CAAC;EAED,MAAM4D,wBAAwB,GAAGA,CAAA,KAAM;IACrC,QAAQ5D,gBAAgB;MACtB,KAAK,CAAC;MACN,KAAK,CAAC;QAAE,OAAO,gBAAgB;MAC/B,KAAK,CAAC;MACN,KAAK,CAAC;QAAE,OAAO,YAAY;MAC3B,KAAK,CAAC;MACN,KAAK,CAAC;QAAE,OAAO,YAAY;MAC3B;QAAS,OAAO,UAAU;IAC5B;EACF,CAAC;EAED,oBACExB,OAAA;IAAKqF,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtCtF,OAAA;MAAKqF,SAAS,EAAC,+DAA+D;MAAAC,QAAA,gBAE5EtF,OAAA;QAAKqF,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BtF,OAAA;UAAIqF,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAAC;QAE9D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL1F,OAAA;UAAGqF,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAC/B/E,WAAW,KAAK,CAAC,GACd,gDAAgD,GAChD;QAAoC;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGN1F,OAAA;QAAKqF,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpDtF,OAAA;UAAKqF,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CtF,OAAA;YAAKqF,SAAS,EAAE,kEACd9E,WAAW,IAAI,CAAC,GACZ,mDAAmD,GACnD,mCAAmC,EACtC;YAAA+E,QAAA,EACA/E,WAAW,GAAG,CAAC,gBAAGP,OAAA,CAACF,IAAI;cAAC0E,IAAI,EAAC,OAAO;cAACmB,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAG;UAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACN1F,OAAA;YAAKqF,SAAS,EAAE,cAAc9E,WAAW,GAAG,CAAC,GAAG,YAAY,GAAG,WAAW;UAAG;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChF1F,OAAA;YAAKqF,SAAS,EAAE,kEACd9E,WAAW,IAAI,CAAC,GACZ,mDAAmD,GACnD,mCAAmC,EACtC;YAAA+E,QAAA,EAAC;UAEJ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1F,OAAA;QAAM4F,QAAQ,EAAE7C,YAAa;QAACsC,SAAS,EAAC,WAAW;QAAAC,QAAA,GAChD/E,WAAW,KAAK,CAAC,iBAChBP,OAAA,CAAAE,SAAA;UAAAoF,QAAA,gBAEEtF,OAAA,CAACJ,KAAK;YACJiG,KAAK,EAAC,WAAW;YACjBb,IAAI,EAAC,MAAM;YACXc,WAAW,EAAC,sBAAsB;YAClCzD,KAAK,EAAE1B,QAAQ,CAACE,QAAS;YACzBkF,QAAQ,EAAG/C,CAAC,IAAKb,iBAAiB,CAAC,UAAU,EAAEa,CAAC,CAACgD,MAAM,CAAC3D,KAAK,CAAE;YAC/DwB,KAAK,EAAEvC,MAAM,CAACT,QAAS;YACvBoF,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEF1F,OAAA,CAACJ,KAAK;YACJiG,KAAK,EAAC,eAAe;YACrBb,IAAI,EAAC,OAAO;YACZc,WAAW,EAAC,0BAA0B;YACtCzD,KAAK,EAAE1B,QAAQ,CAACG,KAAM;YACtBiF,QAAQ,EAAG/C,CAAC,IAAKb,iBAAiB,CAAC,OAAO,EAAEa,CAAC,CAACgD,MAAM,CAAC3D,KAAK,CAAE;YAC5DwB,KAAK,EAAEvC,MAAM,CAACR,KAAM;YACpB2D,WAAW,EAAC,iEAAiE;YAC7EwB,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEF1F,OAAA;YAAKqF,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBtF,OAAA,CAACJ,KAAK;cACJiG,KAAK,EAAC,UAAU;cAChBb,IAAI,EAAC,UAAU;cACfc,WAAW,EAAC,0BAA0B;cACtCzD,KAAK,EAAE1B,QAAQ,CAACI,QAAS;cACzBgF,QAAQ,EAAG/C,CAAC,IAAKb,iBAAiB,CAAC,UAAU,EAAEa,CAAC,CAACgD,MAAM,CAAC3D,KAAK,CAAE;cAC/DwB,KAAK,EAAEvC,MAAM,CAACP,QAAS;cACvBkF,QAAQ;YAAA;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EAED/E,QAAQ,CAACI,QAAQ,iBAChBf,OAAA;cAAKqF,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBtF,OAAA;gBAAKqF,SAAS,EAAC,2CAA2C;gBAAAC,QAAA,gBACxDtF,OAAA;kBAAMqF,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/D1F,OAAA;kBAAMqF,SAAS,EAAE,eACf7D,gBAAgB,IAAI,CAAC,GAAG,kBAAkB,GAC1CA,gBAAgB,IAAI,CAAC,GAAG,cAAc,GAAG,cAAc,EACtD;kBAAA8D,QAAA,EACAH,uBAAuB,CAAC;gBAAC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN1F,OAAA;gBAAKqF,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,eAC/CtF,OAAA;kBACEqF,SAAS,EAAE,gDAAgDD,wBAAwB,CAAC,CAAC,EAAG;kBACxFc,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAI3E,gBAAgB,GAAG,CAAC,GAAI,GAAG;kBAAI;gBAAE;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN1F,OAAA,CAACJ,KAAK;YACJiG,KAAK,EAAC,kBAAkB;YACxBb,IAAI,EAAC,UAAU;YACfc,WAAW,EAAC,uBAAuB;YACnCzD,KAAK,EAAE1B,QAAQ,CAACK,eAAgB;YAChC+E,QAAQ,EAAG/C,CAAC,IAAKb,iBAAiB,CAAC,iBAAiB,EAAEa,CAAC,CAACgD,MAAM,CAAC3D,KAAK,CAAE;YACtEwB,KAAK,EAAEvC,MAAM,CAACN,eAAgB;YAC9BiF,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEF1F,OAAA,CAACL,MAAM;YACLqF,IAAI,EAAC,QAAQ;YACboB,OAAO,EAAEvD,cAAe;YACxBwC,SAAS,EAAC,QAAQ;YAClBgB,QAAQ,EAAC,YAAY;YACrBC,YAAY,EAAC,OAAO;YAAAhB,QAAA,EACrB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CACH,EAEAnF,WAAW,KAAK,CAAC,iBAChBP,OAAA,CAAAE,SAAA;UAAAoF,QAAA,gBAEEtF,OAAA,CAACJ,KAAK;YACJiG,KAAK,EAAC,mBAAmB;YACzBb,IAAI,EAAC,MAAM;YACXc,WAAW,EAAC,8BAA8B;YAC1CzD,KAAK,EAAE1B,QAAQ,CAACM,gBAAiB;YACjC8E,QAAQ,EAAG/C,CAAC,IAAKb,iBAAiB,CAAC,kBAAkB,EAAEa,CAAC,CAACgD,MAAM,CAAC3D,KAAK,CAAE;YACvEwB,KAAK,EAAEvC,MAAM,CAACL,gBAAiB;YAC/BwD,WAAW,EAAC,yCAAyC;YACrDwB,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEF1F,OAAA,CAACJ,KAAK;YACJiG,KAAK,EAAC,qBAAqB;YAC3Bb,IAAI,EAAC,MAAM;YACXc,WAAW,EAAC,aAAa;YACzBzD,KAAK,EAAE1B,QAAQ,CAACO,kBAAmB;YACnC6E,QAAQ,EAAG/C,CAAC,IAAKb,iBAAiB,CAAC,oBAAoB,EAAEa,CAAC,CAACgD,MAAM,CAAC3D,KAAK,CAAE;YACzEwB,KAAK,EAAEvC,MAAM,CAACJ,kBAAmB;YACjCuD,WAAW,EAAC,oDAAoD;YAChEwB,QAAQ;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAGF1F,OAAA;YAAKqF,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBtF,OAAA,CAACH,QAAQ;cACPgG,KAAK,EAAC,iCAAiC;cACvCU,OAAO,EAAE5F,QAAQ,CAACS,YAAa;cAC/B2E,QAAQ,EAAG/C,CAAC,IAAKb,iBAAiB,CAAC,cAAc,EAAEa,CAAC,CAACgD,MAAM,CAACO,OAAO,CAAE;cACrE1C,KAAK,EAAEvC,MAAM,CAACF,YAAa;cAC3B6E,QAAQ;YAAA;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEF1F,OAAA,CAACH,QAAQ;cACPgG,KAAK,EAAC,+BAA+B;cACrCU,OAAO,EAAE5F,QAAQ,CAACU,cAAe;cACjC0E,QAAQ,EAAG/C,CAAC,IAAKb,iBAAiB,CAAC,gBAAgB,EAAEa,CAAC,CAACgD,MAAM,CAACO,OAAO,CAAE;cACvE1C,KAAK,EAAEvC,MAAM,CAACD,cAAe;cAC7B4E,QAAQ;YAAA;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAELpE,MAAM,CAAC4D,MAAM,iBACZlF,OAAA;YAAKqF,SAAS,EAAC,+DAA+D;YAAAC,QAAA,eAC5EtF,OAAA;cAAGqF,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAEhE,MAAM,CAAC4D;YAAM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CACN,eAGD1F,OAAA;YAAKqF,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BtF,OAAA,CAACL,MAAM;cACLqF,IAAI,EAAC,QAAQ;cACbwB,OAAO,EAAC,SAAS;cACjBJ,OAAO,EAAEtD,kBAAmB;cAC5BuC,SAAS,EAAC,QAAQ;cAClBgB,QAAQ,EAAC,WAAW;cACpBC,YAAY,EAAC,MAAM;cAAAhB,QAAA,EACpB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAET1F,OAAA,CAACL,MAAM;cACLqF,IAAI,EAAC,QAAQ;cACbyB,OAAO,EAAEhG,SAAU;cACnB4E,SAAS,EAAC,QAAQ;cAClBgB,QAAQ,EAAC,UAAU;cACnBC,YAAY,EAAC,MAAM;cAAAhB,QAAA,EACpB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA,eACN,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGP1F,OAAA;QAAKqF,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eAC3DtF,OAAA;UAAGqF,SAAS,EAAC,6BAA6B;UAAAC,QAAA,GAAC,0BACjB,EAAC,GAAG,eAC5BtF,OAAA,CAACT,IAAI;YACHmH,EAAE,EAAC,QAAQ;YACXrB,SAAS,EAAC,kEAAkE;YAAAC,QAAA,EAC7E;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1F,OAAA;MAAKqF,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BtF,OAAA;QAAGqF,SAAS,EAAC,6BAA6B;QAAAC,QAAA,GAAC,oFAEzC,eAAAtF,OAAA;UAAAuF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,wFAER;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtF,EAAA,CA3cID,gBAAgB;EAAA,QACHX,WAAW,EACPC,OAAO;AAAA;AAAAkH,EAAA,GAFxBxG,gBAAgB;AA6ctB,eAAeA,gBAAgB;AAAC,IAAAwG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}